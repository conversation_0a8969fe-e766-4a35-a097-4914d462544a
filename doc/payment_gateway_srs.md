
# Payment Gateway System - Software Requirements Specification (SRS)

## 1. Overview

**System Purpose:** To provide a secure and efficient platform for managing payments, bank accounts, merchants, agents, and internal transfers, integrated via APIs with external systems.

**Technology Stack:**

- Backend: Java (Spring Boot)
- Frontend: React/JavaScript
- Database: CockroachDB
- Hosting: AWS
- Deployment: Docker + Kubernetes
- Authentication: Google Authenticator (TOTP), session timeout, CAPTCHA

---

## 2. Functional Requirements

### 2.1 User Management

- Role-based access control via UI
- Create/edit/delete roles
- Assign roles to users
- User registration & login with TOTP 2FA (Google Authenticator)
- Password reset after login
- Full audit logs for user actions

### 2.2 Bank Account Management

- Create/edit/delete bank accounts (visible to roles with explicit permission)
- Set account properties: nickname, in/out transaction limits, balance limits
- Note: Merchants and agents cannot view or manage bank accounts

### 2.3 Monitoring (API Integration)

- REST API endpoint for incoming transaction recording (authenticated via API Key)
- REST API endpoint for merchants to notify expected payments
- Support for webhook callbacks for real-time bank updates
- Reconciliation logic:
  - External systems trigger reconciliation API
  - System records last reconciliation timestamp
  - Display warning if reconciliation overdue

### 2.4 Exception Processing

- Exception types:
  - Reconciliation mismatches
  - Unrecognized bank transaction (missing merchant input)
  - Unpaid merchant transaction (missing bank transaction)
- UI for manual exception resolution and notes
- Allow manual input of missing transactions

### 2.5 Reporting

- Reports downloadable in CSV
- Reports by role:
  - Merchant: own transactions, payouts, balances
  - Agent: own data + their merchants’ data
  - Admin: all data
- Suggested Reports:
  - Transaction Report (date, type, status, amount)
  - Reconciliation Report
  - Payout Report
  - Commission Report
  - Agent Earnings
  - Balance Overview

### 2.6 Internal Transfers

- Transfer funds between merchants
- Transfer funds between bank accounts
- No approval or limits for now

### 2.7 Merchant Management

- Manage merchants within same portal
- Assign commissions per transaction (incoming & outgoing)
- Track merchant balances
- Payout flow:
  - Merchant sends payout API request
  - Operator sees the request
  - Manual transfer done
  - Operator confirms payout
  - System sends confirmation callback to merchant

### 2.8 Agent Management

- One agent can manage multiple merchants
- Agents earn commission per transaction
- Tiered commission structure (agent earns difference between platform and merchant rate)

### 2.9 Transaction Flow

#### Happy Case:

- Bank system sends in-transaction via API
- Merchant system sends expected transaction via API
- System matches both entries:
  - Books the transaction under the corresponding merchant
  - Increases merchant balance
  - Books commission to agent and platform

#### Unhappy Case 1: Missing Merchant Input

- Bank system sends in-transaction
- No expected transaction found from merchant
- System raises exception: **Unrecognized Transaction**

#### Unhappy Case 2: Missing Bank Transaction

- Merchant system sends expected transaction
- No matching bank transaction received
- System raises exception: **Unpaid Transaction**

---

## 3. Non-Functional Requirements

### 3.1 Security

- Sensitive data encrypted at rest and in transit
- TOTP-based 2FA (Google Authenticator)
- CAPTCHA on login
- Session timeout for inactivity
- API authentication via API key

### 3.2 Infrastructure

- Hosted on AWS
- Use of CockroachDB
- Dockerized services
- Orchestration with Kubernetes

---

## 4. System Diagrams

### 4.1 System Architecture Diagram

```plaintext
+-------------------+       +------------------------+       +-------------------+
|  Merchant System  | <---> | Payment Gateway System | <---> |   Bank System     |
+-------------------+       +------------------------+       +-------------------+
                                      |
                                      v
                            +---------------------+
                            |   CockroachDB       |
                            +---------------------+
                                      |
                                      v
                            +---------------------+
                            | React Admin Panel   |
                            +---------------------+
```

### 4.2 Payout Flow

```plaintext
+-----------------+         +----------------+         +------------------+
| Merchant System | --->    |   Payout API   | --->    |   Operator UI    |
+-----------------+         +----------------+         +------------------+
                                                         |        |
                                                         v        v
                                               [Manual Bank Transfer]
                                                         |
                                                         v
                                              [Confirm Button Clicked]
                                                         |
                                                         v
                                         [Callback Sent to Merchant System]
```

### 4.3 Reconciliation Flow

```plaintext
+----------------+       +------------------------+       +----------------+
| Bank System    | --->  |  Incoming Txn API      | --->  |  Transaction DB|
+----------------+       +------------------------+       +----------------+
                                                                   |
                                                                   v
                                                          [Balance Update]
                                                                   |
                                                                   v
                                                          [Balance Comparison]
                                                                   |
                                                                   v
                                                     [Raise Exception if mismatch]
```

### 4.4 Transaction Flow (Happy & Unhappy Cases)

```plaintext
        +--------------------+               +--------------------------+
        |  Bank Transaction  | + Match with  |  Merchant Expected Txn   |
        +--------------------+               +--------------------------+
                        |                                 |
                        v                                 v
                  [Transaction Booked]       [Credit Merchant Balance]
                        |                                 |
                        +-----------> [Commission Recorded (Agent + Platform)]

Unhappy Case 1: Unrecognized Transaction
    +--------------------+   NO MATCH   X   [No Merchant Input]
    | Bank Transaction   | ------------->   Raise: Unrecognized Transaction
    +--------------------+

Unhappy Case 2: Unpaid Transaction
    [No Bank Txn]    X   MATCH   +--------------------------+
                      <-------- |  Merchant Expected Txn    |
                                 +--------------------------+
                                 Raise: Unpaid Transaction
```

---

## 5. Sample Role & Permission Matrix

| Page / Feature           | Admin | Operator | Merchant | Agent |
| ------------------------ | ----- | -------- | -------- | ----- |
| View Dashboard           | ✅     | ✅        | ✅        | ✅     |
| Manage Users             | ✅     | ❌        | ❌        | ❌     |
| Manage Roles             | ✅     | ❌        | ❌        | ❌     |
| Create Bank Account      | ✅     | ✅        | ❌        | ❌     |
| View Bank Accounts       | ✅     | ✅        | ❌        | ❌     |
| API Key Management       | ✅     | ✅        | ❌        | ❌     |
| Create Merchant          | ✅     | ✅        | ❌        | ❌     |
| View Own Transactions    | ✅     | ✅        | ✅        | ✅     |
| View Merchant Data       | ✅     | ✅        | ✅        | ✅     |
| View Agent Commission    | ✅     | ✅        | ❌        | ✅     |
| View Reports             | ✅     | ✅        | ✅        | ✅     |
| Manual Payout Processing | ✅     | ✅        | ❌        | ❌     |
| Exception Resolution     | ✅     | ✅        | ❌        | ❌     |

---

## 6. API Notes

- All APIs must be documented with Swagger/OpenAPI
- Incoming and merchant APIs must require API Key authentication
- Include rate limiting on public-facing endpoints
- Each merchant/agent should have a unique API key with scopes

---

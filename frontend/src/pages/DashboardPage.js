import React from 'react';
import { Row, Col, Card, Statistic, Typography, Spin, Alert } from 'antd';
import {
  TransactionOutlined,
  ShopOutlined,
  TeamOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { apiService } from '../services/apiService';
import { TransactionChart } from '../components/Charts/TransactionChart';
import { CommissionChart } from '../components/Charts/CommissionChart';
import { RecentTransactions } from '../components/Dashboard/RecentTransactions';
import { RecentExceptions } from '../components/Dashboard/RecentExceptions';

const { Title, Text } = Typography;

export function DashboardPage() {
  const { data: stats, isLoading, error } = useQuery(
    'dashboardStats',
    () => apiService.getDashboardStats(),
    {
      refetchInterval: 30000, // Refresh every 30 seconds
    }
  );

  if (isLoading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Error Loading Dashboard"
        description="Failed to load dashboard statistics. Please try again."
        type="error"
        showIcon
      />
    );
  }

  const statsData = stats?.data || {};

  const statCards = [
    {
      title: 'Total Transactions',
      value: statsData.totalTransactions || 0,
      icon: <TransactionOutlined />,
      color: '#1890ff',
      type: 'primary',
    },
    {
      title: 'Pending Transactions',
      value: statsData.pendingTransactions || 0,
      icon: <ClockCircleOutlined />,
      color: '#faad14',
      type: 'warning',
    },
    {
      title: 'Matched Transactions',
      value: statsData.matchedTransactions || 0,
      icon: <CheckCircleOutlined />,
      color: '#52c41a',
      type: 'success',
    },
    {
      title: 'Open Exceptions',
      value: statsData.openExceptions || 0,
      icon: <ExclamationCircleOutlined />,
      color: '#ff4d4f',
      type: 'danger',
    },
    {
      title: 'Active Merchants',
      value: statsData.totalMerchants || 0,
      icon: <ShopOutlined />,
      color: '#722ed1',
      type: 'primary',
    },
    {
      title: 'Active Agents',
      value: statsData.totalAgents || 0,
      icon: <TeamOutlined />,
      color: '#13c2c2',
      type: 'primary',
    },
    {
      title: 'Platform Commission',
      value: statsData.totalPlatformCommission || 0,
      icon: <DollarOutlined />,
      color: '#eb2f96',
      type: 'primary',
      prefix: '$',
      precision: 2,
    },
    {
      title: 'Exception Transactions',
      value: statsData.exceptionTransactions || 0,
      icon: <WarningOutlined />,
      color: '#fa8c16',
      type: 'warning',
    },
  ];

  return (
    <div className="fade-in">
      <div className="page-header">
        <Title level={2} className="page-title">
          Dashboard
        </Title>
        <Text className="page-description">
          Overview of your payment gateway system
        </Text>
      </div>

      {/* Statistics Cards */}
      <div className="stats-grid">
        {statCards.map((stat, index) => (
          <Card key={index} className={`stat-card ${stat.type}`} hoverable>
            <div className="stat-header">
              <div className={`stat-icon ${stat.type}`}>
                {stat.icon}
              </div>
            </div>
            <Statistic
              value={stat.value}
              prefix={stat.prefix}
              precision={stat.precision}
              valueStyle={{ 
                color: stat.color,
                fontSize: '28px',
                fontWeight: 'bold',
              }}
            />
            <div className="stat-label">{stat.title}</div>
          </Card>
        ))}
      </div>

      {/* Charts Section */}
      <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={12}>
          <Card title="Transaction Volume" className="chart-container">
            <TransactionChart />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Commission Overview" className="chart-container">
            <CommissionChart />
          </Card>
        </Col>
      </Row>

      {/* Recent Activity Section */}
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="Recent Transactions" className="table-container">
            <RecentTransactions />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Recent Exceptions" className="table-container">
            <RecentExceptions />
          </Card>
        </Col>
      </Row>
    </div>
  );
}

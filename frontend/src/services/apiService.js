import axios from 'axios';
import { message } from 'antd';
import Cookies from 'js-cookie';

// Create axios instance
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Unauthorized - redirect to login
      Cookies.remove('auth_token');
      window.location.href = '/login';
    } else if (error.response?.status === 403) {
      // Forbidden
      message.error('You do not have permission to perform this action');
    } else if (error.response?.status >= 500) {
      // Server error
      message.error('Server error. Please try again later.');
    }
    return Promise.reject(error);
  }
);

export const apiService = {
  // Auth methods
  setAuthToken: (token) => {
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  },
  
  removeAuthToken: () => {
    delete api.defaults.headers.common['Authorization'];
  },

  // Generic HTTP methods
  get: (url, config) => api.get(url, config),
  post: (url, data, config) => api.post(url, data, config),
  put: (url, data, config) => api.put(url, data, config),
  delete: (url, config) => api.delete(url, config),
  patch: (url, data, config) => api.patch(url, data, config),

  // Authentication
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  refreshToken: () => api.post('/auth/refresh'),
  getCurrentUser: () => api.get('/auth/me'),

  // Dashboard
  getDashboardStats: () => api.get('/reports/dashboard/stats'),

  // Transactions
  getTransactions: (params) => api.get('/transactions', { params }),
  getTransactionByReference: (reference) => api.get(`/transactions/${reference}`),
  createExpectedTransaction: (data) => api.post('/transactions/expected', data),
  processBankTransaction: (data) => api.post('/transactions/bank', data),
  matchTransaction: (transactionId) => api.post(`/transactions/${transactionId}/match`),

  // Merchants
  getMerchants: (params) => api.get('/merchants', { params }),
  getMerchantById: (id) => api.get(`/merchants/${id}`),
  createMerchant: (data) => api.post('/merchants', data),
  updateMerchant: (id, data) => api.put(`/merchants/${id}`, data),
  deleteMerchant: (id) => api.delete(`/merchants/${id}`),

  // Agents
  getAgents: (params) => api.get('/agents', { params }),
  getAgentById: (id) => api.get(`/agents/${id}`),
  createAgent: (data) => api.post('/agents', data),
  updateAgent: (id, data) => api.put(`/agents/${id}`, data),
  deleteAgent: (id) => api.delete(`/agents/${id}`),

  // Bank Accounts
  getBankAccounts: (params) => api.get('/bank-accounts', { params }),
  getBankAccountById: (id) => api.get(`/bank-accounts/${id}`),
  createBankAccount: (data) => api.post('/bank-accounts', data),
  updateBankAccount: (id, data) => api.put(`/bank-accounts/${id}`, data),
  deleteBankAccount: (id) => api.delete(`/bank-accounts/${id}`),

  // Payouts
  getPayouts: (params) => api.get('/payouts', { params }),
  getPayoutById: (id) => api.get(`/payouts/${id}`),
  createPayout: (data) => api.post('/payouts', data),
  processPayout: (id, data) => api.post(`/payouts/${id}/process`, data),
  confirmPayout: (id, data) => api.post(`/payouts/${id}/confirm`, data),
  rejectPayout: (id, reason) => api.post(`/payouts/${id}/reject`, reason),
  cancelPayout: (id) => api.post(`/payouts/${id}/cancel`),

  // Exceptions
  getExceptions: (params) => api.get('/exceptions', { params }),
  getExceptionById: (id) => api.get(`/exceptions/${id}`),
  resolveException: (id, data) => api.post(`/exceptions/${id}/resolve`, data),
  markExceptionInProgress: (id) => api.post(`/exceptions/${id}/in-progress`),
  closeException: (id, reason) => api.post(`/exceptions/${id}/close`, reason),
  detectUnpaidTransactions: () => api.post('/exceptions/detect-unpaid'),

  // Reports
  exportTransactionReport: (params) => api.get('/reports/transactions/export', { 
    params, 
    responseType: 'blob' 
  }),
  exportCommissionReport: (params) => api.get('/reports/commissions/export', { 
    params, 
    responseType: 'blob' 
  }),
  exportMerchantSummaryReport: (params) => api.get('/reports/merchants/export', { 
    params, 
    responseType: 'blob' 
  }),
  exportAgentSummaryReport: (params) => api.get('/reports/agents/export', { 
    params, 
    responseType: 'blob' 
  }),
  exportExceptionReport: (params) => api.get('/reports/exceptions/export', { 
    params, 
    responseType: 'blob' 
  }),

  // Users
  getUsers: (params) => api.get('/users', { params }),
  getUserById: (id) => api.get(`/users/${id}`),
  createUser: (data) => api.post('/users', data),
  updateUser: (id, data) => api.put(`/users/${id}`, data),
  deleteUser: (id) => api.delete(`/users/${id}`),

  // Roles
  getRoles: () => api.get('/roles'),
  getPermissions: () => api.get('/permissions'),
};

// Utility function to download blob response as file
export const downloadFile = (blob, filename) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

export default apiService;

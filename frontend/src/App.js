import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from 'antd';
import { AuthProvider } from './contexts/AuthContext';
import { ProtectedRoute } from './components/ProtectedRoute';
import { AppHeader } from './components/Layout/AppHeader';
import { AppSidebar } from './components/Layout/AppSidebar';
import { LoginPage } from './pages/LoginPage';
import { DashboardPage } from './pages/DashboardPage';
import { TransactionsPage } from './pages/TransactionsPage';
import { MerchantsPage } from './pages/MerchantsPage';
import { AgentsPage } from './pages/AgentsPage';
import { PayoutsPage } from './pages/PayoutsPage';
import { ExceptionsPage } from './pages/ExceptionsPage';
import { ReportsPage } from './pages/ReportsPage';
import { UsersPage } from './pages/UsersPage';
import { BankAccountsPage } from './pages/BankAccountsPage';
import { ProfilePage } from './pages/ProfilePage';
import './App.css';

const { Content, Sider } = Layout;

function App() {
  return (
    <AuthProvider>
      <div className="App">
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <MainLayout />
              </ProtectedRoute>
            }
          />
        </Routes>
      </div>
    </AuthProvider>
  );
}

function MainLayout() {
  const [collapsed, setCollapsed] = React.useState(false);

  return (
    <Layout className="app-layout">
      <AppHeader />
      <Layout>
        <Sider
          collapsible
          collapsed={collapsed}
          onCollapse={setCollapsed}
          width={250}
          theme="light"
          style={{
            overflow: 'auto',
            height: 'calc(100vh - 64px)',
            position: 'fixed',
            left: 0,
            top: 64,
            bottom: 0,
          }}
        >
          <AppSidebar />
        </Sider>
        <Layout style={{ marginLeft: collapsed ? 80 : 250, transition: 'margin-left 0.2s' }}>
          <Content className="app-content">
            <Routes>
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="/dashboard" element={<DashboardPage />} />
              <Route path="/transactions" element={<TransactionsPage />} />
              <Route path="/merchants" element={<MerchantsPage />} />
              <Route path="/agents" element={<AgentsPage />} />
              <Route path="/payouts" element={<PayoutsPage />} />
              <Route path="/exceptions" element={<ExceptionsPage />} />
              <Route path="/reports" element={<ReportsPage />} />
              <Route path="/users" element={<UsersPage />} />
              <Route path="/bank-accounts" element={<BankAccountsPage />} />
              <Route path="/profile" element={<ProfilePage />} />
            </Routes>
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
}

export default App;

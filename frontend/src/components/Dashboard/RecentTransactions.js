import React from 'react';
import { Table, Tag, Typography, Button } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import moment from 'moment';

const { Text } = Typography;

// Mock data for demonstration
const mockTransactions = [
  {
    id: '1',
    transactionReference: 'TXN1704067200ABC123',
    merchantName: 'E-Commerce Store',
    amount: 1250.00,
    status: 'COMPLETED',
    transactionDate: '2024-01-01T10:30:00',
    type: 'INCOMING',
  },
  {
    id: '2',
    transactionReference: 'TXN1704067300DEF456',
    merchantName: 'Online Marketplace',
    amount: 850.50,
    status: 'PENDING',
    transactionDate: '2024-01-01T11:15:00',
    type: 'INCOMING',
  },
  {
    id: '3',
    transactionReference: 'TXN1704067400GHI789',
    merchantName: 'Digital Services',
    amount: 2100.75,
    status: 'MATCHED',
    transactionDate: '2024-01-01T12:00:00',
    type: 'INCOMING',
  },
  {
    id: '4',
    transactionReference: 'TXN1704067500JKL012',
    merchantName: 'Retail Chain',
    amount: 675.25,
    status: 'PROCESSING',
    transactionDate: '2024-01-01T12:45:00',
    type: 'OUTGOING',
  },
  {
    id: '5',
    transactionReference: 'TXN1704067600MNO345',
    merchantName: 'Tech Startup',
    amount: 1450.00,
    status: 'EXCEPTION',
    transactionDate: '2024-01-01T13:30:00',
    type: 'INCOMING',
  },
];

export function RecentTransactions() {
  const navigate = useNavigate();

  const getStatusColor = (status) => {
    const colors = {
      PENDING: 'orange',
      PROCESSING: 'blue',
      COMPLETED: 'green',
      MATCHED: 'green',
      FAILED: 'red',
      EXCEPTION: 'red',
      CANCELLED: 'default',
    };
    return colors[status] || 'default';
  };

  const getTypeColor = (type) => {
    const colors = {
      INCOMING: 'green',
      OUTGOING: 'blue',
      PAYOUT: 'purple',
      INTERNAL_TRANSFER: 'cyan',
    };
    return colors[type] || 'default';
  };

  const columns = [
    {
      title: 'Reference',
      dataIndex: 'transactionReference',
      key: 'transactionReference',
      render: (text) => (
        <Text code style={{ fontSize: '12px' }}>
          {text.substring(0, 16)}...
        </Text>
      ),
    },
    {
      title: 'Merchant',
      dataIndex: 'merchantName',
      key: 'merchantName',
      render: (text) => (
        <Text style={{ fontSize: '12px' }}>{text}</Text>
      ),
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => (
        <Text strong style={{ fontSize: '12px' }}>
          ${amount.toLocaleString()}
        </Text>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={getTypeColor(type)} style={{ fontSize: '10px' }}>
          {type}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)} style={{ fontSize: '10px' }}>
          {status}
        </Tag>
      ),
    },
    {
      title: 'Date',
      dataIndex: 'transactionDate',
      key: 'transactionDate',
      render: (date) => (
        <Text style={{ fontSize: '11px' }}>
          {moment(date).format('MMM DD, HH:mm')}
        </Text>
      ),
    },
    {
      title: 'Action',
      key: 'action',
      render: (_, record) => (
        <Button
          type="text"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => navigate(`/transactions?search=${record.transactionReference}`)}
        />
      ),
    },
  ];

  return (
    <div>
      <Table
        dataSource={mockTransactions}
        columns={columns}
        pagination={false}
        size="small"
        rowKey="id"
        scroll={{ x: true }}
      />
      <div style={{ textAlign: 'center', marginTop: '16px' }}>
        <Button type="link" onClick={() => navigate('/transactions')}>
          View All Transactions
        </Button>
      </div>
    </div>
  );
}

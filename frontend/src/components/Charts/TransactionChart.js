import React from 'react';
import { Line<PERSON><PERSON>, Line, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Spin } from 'antd';

// Mock data for demonstration
const mockData = [
  { date: '2024-01-01', transactions: 45, amount: 12500 },
  { date: '2024-01-02', transactions: 52, amount: 15200 },
  { date: '2024-01-03', transactions: 38, amount: 9800 },
  { date: '2024-01-04', transactions: 61, amount: 18900 },
  { date: '2024-01-05', transactions: 47, amount: 13400 },
  { date: '2024-01-06', transactions: 55, amount: 16700 },
  { date: '2024-01-07', transactions: 43, amount: 11200 },
  { date: '2024-01-08', transactions: 58, amount: 17800 },
  { date: '2024-01-09', transactions: 49, amount: 14300 },
  { date: '2024-01-10', transactions: 62, amount: 19500 },
];

export function TransactionChart() {
  const [loading, setLoading] = React.useState(true);
  const [data, setData] = React.useState([]);

  React.useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setData(mockData);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <Spin size="large" />
      </div>
    );
  }

  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const formatAmount = (value) => {
    return `$${value.toLocaleString()}`;
  };

  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="date" 
          tickFormatter={formatDate}
          stroke="#8c8c8c"
          fontSize={12}
        />
        <YAxis 
          yAxisId="left"
          stroke="#1890ff"
          fontSize={12}
        />
        <YAxis 
          yAxisId="right" 
          orientation="right"
          stroke="#52c41a"
          fontSize={12}
        />
        <Tooltip 
          labelFormatter={(value) => `Date: ${formatDate(value)}`}
          formatter={(value, name) => [
            name === 'transactions' ? value : formatAmount(value),
            name === 'transactions' ? 'Transactions' : 'Amount'
          ]}
          contentStyle={{
            backgroundColor: 'white',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          }}
        />
        <Line 
          yAxisId="left"
          type="monotone" 
          dataKey="transactions" 
          stroke="#1890ff" 
          strokeWidth={2}
          dot={{ fill: '#1890ff', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: '#1890ff', strokeWidth: 2 }}
        />
        <Line 
          yAxisId="right"
          type="monotone" 
          dataKey="amount" 
          stroke="#52c41a" 
          strokeWidth={2}
          dot={{ fill: '#52c41a', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: '#52c41a', strokeWidth: 2 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}

import React from 'react';
import { Menu } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  TransactionOutlined,
  ShopOutlined,
  TeamOutlined,
  BankOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  UserOutlined,
  SettingOutlined,
  CreditCardOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';

export function AppSidebar() {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission } = useAuth();

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: 'Dashboard',
      permission: 'DASHBOARD_VIEW',
    },
    {
      key: '/transactions',
      icon: <TransactionOutlined />,
      label: 'Transactions',
      permission: 'TRANSACTION_VIEW',
    },
    {
      key: '/merchants',
      icon: <ShopOutlined />,
      label: 'Merchants',
      permission: 'MERCHANT_VIEW',
    },
    {
      key: '/agents',
      icon: <TeamOutlined />,
      label: 'Agents',
      permission: 'AGENT_VIEW',
    },
    {
      key: '/payouts',
      icon: <CreditCardOutlined />,
      label: 'Payouts',
      permission: 'PAYOUT_VIEW',
    },
    {
      key: '/exceptions',
      icon: <ExclamationCircleOutlined />,
      label: 'Exceptions',
      permission: 'EXCEPTION_VIEW',
    },
    {
      key: '/reports',
      icon: <FileTextOutlined />,
      label: 'Reports',
      permission: 'REPORT_VIEW',
    },
    {
      type: 'divider',
    },
    {
      key: 'management',
      label: 'Management',
      type: 'group',
    },
    {
      key: '/bank-accounts',
      icon: <BankOutlined />,
      label: 'Bank Accounts',
      permission: 'BANK_ACCOUNT_VIEW',
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: 'Users',
      permission: 'USER_VIEW',
    },
  ];

  // Filter menu items based on permissions
  const filteredItems = menuItems.filter(item => {
    if (item.type === 'divider' || item.type === 'group') {
      return true;
    }
    return !item.permission || hasPermission(item.permission);
  });

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  return (
    <div style={{ padding: '16px 0' }}>
      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        items={filteredItems}
        onClick={handleMenuClick}
        style={{ border: 'none' }}
      />
    </div>
  );
}

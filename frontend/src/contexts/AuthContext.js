import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { message } from 'antd';
import { apiService } from '../services/apiService';
import Cookies from 'js-cookie';

const AuthContext = createContext();

const initialState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: true,
  permissions: [],
  roles: [],
};

function authReducer(state, action) {
  switch (action.type) {
    case 'LOGIN_START':
      return {
        ...state,
        isLoading: true,
      };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        permissions: action.payload.permissions || [],
        roles: action.payload.roles || [],
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        permissions: [],
        roles: [],
      };
    case 'LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    default:
      return state;
  }
}

export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    // Check for existing token on app load
    const token = Cookies.get('auth_token');
    if (token) {
      // Validate token and get user info
      validateToken(token);
    } else {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  const validateToken = async (token) => {
    try {
      // Set token in API service
      apiService.setAuthToken(token);
      
      // Get current user info
      const response = await apiService.get('/auth/me');
      
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user: response.data,
          token,
          permissions: response.data.permissions || [],
          roles: response.data.roles || [],
        },
      });
    } catch (error) {
      console.error('Token validation failed:', error);
      Cookies.remove('auth_token');
      apiService.removeAuthToken();
      dispatch({ type: 'LOGIN_FAILURE' });
    }
  };

  const login = async (credentials) => {
    try {
      dispatch({ type: 'LOGIN_START' });
      
      const response = await apiService.post('/auth/login', credentials);
      const { token, username, email, fullName, roles, permissions } = response.data;
      
      // Store token in cookie (expires in 2 hours to match backend)
      Cookies.set('auth_token', token, { expires: 1/12 }); // 2 hours
      
      // Set token in API service
      apiService.setAuthToken(token);
      
      const user = {
        username,
        email,
        fullName,
      };
      
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user,
          token,
          permissions: permissions || [],
          roles: roles || [],
        },
      });
      
      message.success('Login successful!');
      return { success: true };
    } catch (error) {
      dispatch({ type: 'LOGIN_FAILURE' });
      const errorMessage = error.response?.data?.message || 'Login failed';
      message.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const logout = async () => {
    try {
      // Call logout endpoint
      await apiService.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local state regardless of API call result
      Cookies.remove('auth_token');
      apiService.removeAuthToken();
      dispatch({ type: 'LOGOUT' });
      message.success('Logged out successfully');
    }
  };

  const hasPermission = (permission) => {
    return state.permissions.includes(permission);
  };

  const hasRole = (role) => {
    return state.roles.includes(role);
  };

  const hasAnyPermission = (permissions) => {
    return permissions.some(permission => state.permissions.includes(permission));
  };

  const hasAnyRole = (roles) => {
    return roles.some(role => state.roles.includes(role));
  };

  const value = {
    ...state,
    login,
    logout,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAnyRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

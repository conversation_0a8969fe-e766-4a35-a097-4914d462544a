spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        
  h2:
    console:
      enabled: true

jwt:
  secret: testSecretKeyForJWTTokenGeneration
  expiration: 3600000

logging:
  level:
    com.paymentgateway: DEBUG
    org.springframework.security: DEBUG

package com.paymentgateway.service;

import com.paymentgateway.entity.*;
import com.paymentgateway.enums.TransactionStatus;
import com.paymentgateway.enums.TransactionType;
import com.paymentgateway.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TransactionServiceTest {

    @Mock
    private TransactionRepository transactionRepository;

    @Mock
    private MerchantRepository merchantRepository;

    @Mock
    private AgentRepository agentRepository;

    @Mock
    private BankAccountRepository bankAccountRepository;

    @Mock
    private CommissionService commissionService;

    @Mock
    private ExceptionService exceptionService;

    @Mock
    private AuditService auditService;

    @InjectMocks
    private TransactionService transactionService;

    private User testUser;
    private Merchant testMerchant;
    private Agent testAgent;
    private BankAccount testBankAccount;

    @BeforeEach
    void setUp() {
        testUser = new User("testuser", "<EMAIL>", "password");
        
        testAgent = new Agent("AGENT001", "Test Agent");
        testAgent.setIncomingCommissionRate(new BigDecimal("0.5"));
        testAgent.setOutgoingCommissionRate(new BigDecimal("0.3"));

        testMerchant = new Merchant("MERCHANT001", "Test Merchant");
        testMerchant.setIncomingCommissionRate(new BigDecimal("0.8"));
        testMerchant.setOutgoingCommissionRate(new BigDecimal("0.6"));
        testMerchant.setAgent(testAgent);
        testMerchant.setIsActive(true);

        testBankAccount = new BankAccount("*********", "Test Account", "Test Bank");
        testBankAccount.setCurrentBalance(new BigDecimal("10000.00"));
        testBankAccount.setIsActive(true);
    }

    @Test
    void testCreateExpectedTransaction_Success() {
        // Given
        String merchantCode = "MERCHANT001";
        String externalRef = "EXT123";
        BigDecimal amount = new BigDecimal("1000.00");
        String description = "Test transaction";

        when(merchantRepository.findByMerchantCode(merchantCode)).thenReturn(Optional.of(testMerchant));
        when(transactionRepository.existsByExternalReference(externalRef)).thenReturn(false);
        when(transactionRepository.save(any(Transaction.class))).thenAnswer(invocation -> {
            Transaction transaction = invocation.getArgument(0);
            transaction.setId(java.util.UUID.randomUUID());
            return transaction;
        });

        // When
        Transaction result = transactionService.createExpectedTransaction(
                merchantCode, externalRef, amount, description, testUser);

        // Then
        assertNotNull(result);
        assertEquals(TransactionType.INCOMING, result.getType());
        assertEquals(TransactionStatus.PENDING, result.getStatus());
        assertEquals(amount, result.getAmount());
        assertEquals(externalRef, result.getExternalReference());
        assertEquals(description, result.getDescription());
        assertEquals(testMerchant, result.getMerchant());
        assertEquals(testAgent, result.getAgent());
        assertTrue(result.getIsExpected());
        assertFalse(result.getIsBankConfirmed());

        verify(transactionRepository).save(any(Transaction.class));
        verify(auditService).logUserAction(eq(testUser), eq("CREATE"), eq("Transaction"), 
                any(), contains("Created expected transaction"));
    }

    @Test
    void testCreateExpectedTransaction_MerchantNotFound() {
        // Given
        String merchantCode = "NONEXISTENT";
        when(merchantRepository.findByMerchantCode(merchantCode)).thenReturn(Optional.empty());

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                transactionService.createExpectedTransaction(merchantCode, "EXT123", 
                        new BigDecimal("1000.00"), "Test", testUser));

        assertEquals("Merchant not found: NONEXISTENT", exception.getMessage());
        verify(transactionRepository, never()).save(any());
    }

    @Test
    void testCreateExpectedTransaction_InactiveMerchant() {
        // Given
        testMerchant.setIsActive(false);
        when(merchantRepository.findByMerchantCode("MERCHANT001")).thenReturn(Optional.of(testMerchant));

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                transactionService.createExpectedTransaction("MERCHANT001", "EXT123", 
                        new BigDecimal("1000.00"), "Test", testUser));

        assertEquals("Merchant is not active: MERCHANT001", exception.getMessage());
    }

    @Test
    void testCreateExpectedTransaction_DuplicateExternalReference() {
        // Given
        String externalRef = "DUPLICATE123";
        when(merchantRepository.findByMerchantCode("MERCHANT001")).thenReturn(Optional.of(testMerchant));
        when(transactionRepository.existsByExternalReference(externalRef)).thenReturn(true);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                transactionService.createExpectedTransaction("MERCHANT001", externalRef, 
                        new BigDecimal("1000.00"), "Test", testUser));

        assertEquals("Duplicate external reference: DUPLICATE123", exception.getMessage());
    }

    @Test
    void testProcessBankTransaction_Success() {
        // Given
        String bankAccountNumber = "*********";
        BigDecimal amount = new BigDecimal("1000.00");
        String bankReference = "BANK123";
        LocalDateTime transactionDate = LocalDateTime.now();
        String description = "Bank transfer";

        when(bankAccountRepository.findByAccountNumber(bankAccountNumber)).thenReturn(Optional.of(testBankAccount));
        when(transactionRepository.save(any(Transaction.class))).thenAnswer(invocation -> {
            Transaction transaction = invocation.getArgument(0);
            transaction.setId(java.util.UUID.randomUUID());
            return transaction;
        });

        // When
        Transaction result = transactionService.processBankTransaction(
                bankAccountNumber, amount, bankReference, transactionDate, description, testUser);

        // Then
        assertNotNull(result);
        assertEquals(TransactionType.INCOMING, result.getType());
        assertEquals(TransactionStatus.PROCESSING, result.getStatus());
        assertEquals(amount, result.getAmount());
        assertEquals(bankReference, result.getExternalReference());
        assertEquals(description, result.getDescription());
        assertEquals(testBankAccount, result.getBankAccount());
        assertFalse(result.getIsExpected());
        assertTrue(result.getIsBankConfirmed());

        // Verify bank account balance was updated
        verify(bankAccountRepository).save(testBankAccount);
        assertEquals(new BigDecimal("11000.00"), testBankAccount.getCurrentBalance());

        verify(transactionRepository).save(any(Transaction.class));
        verify(auditService).logUserAction(eq(testUser), eq("CREATE"), eq("Transaction"), 
                any(), contains("Processed bank transaction"));
    }

    @Test
    void testProcessBankTransaction_BankAccountNotFound() {
        // Given
        String bankAccountNumber = "NONEXISTENT";
        when(bankAccountRepository.findByAccountNumber(bankAccountNumber)).thenReturn(Optional.empty());

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                transactionService.processBankTransaction(bankAccountNumber, new BigDecimal("1000.00"), 
                        "BANK123", LocalDateTime.now(), "Test", testUser));

        assertEquals("Bank account not found: NONEXISTENT", exception.getMessage());
    }

    @Test
    void testProcessBankTransaction_InactiveBankAccount() {
        // Given
        testBankAccount.setIsActive(false);
        when(bankAccountRepository.findByAccountNumber("*********")).thenReturn(Optional.of(testBankAccount));

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                transactionService.processBankTransaction("*********", new BigDecimal("1000.00"), 
                        "BANK123", LocalDateTime.now(), "Test", testUser));

        assertEquals("Bank account is not active: *********", exception.getMessage());
    }

    @Test
    void testGenerateTransactionReference_IsUnique() {
        // When
        String ref1 = invokeGenerateTransactionReference();
        String ref2 = invokeGenerateTransactionReference();

        // Then
        assertNotNull(ref1);
        assertNotNull(ref2);
        assertNotEquals(ref1, ref2);
        assertTrue(ref1.startsWith("TXN"));
        assertTrue(ref2.startsWith("TXN"));
    }

    // Helper method to access private method via reflection for testing
    private String invokeGenerateTransactionReference() {
        try {
            var method = TransactionService.class.getDeclaredMethod("generateTransactionReference");
            method.setAccessible(true);
            return (String) method.invoke(transactionService);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

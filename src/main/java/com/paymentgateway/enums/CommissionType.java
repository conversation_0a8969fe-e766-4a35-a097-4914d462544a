package com.paymentgateway.enums;

public enum CommissionType {
    INCOMING("Incoming Commission"),
    OUTGOING("Outgoing Commission");

    private final String displayName;

    CommissionType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String toString() {
        return displayName;
    }
}

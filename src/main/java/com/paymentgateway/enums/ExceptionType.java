package com.paymentgateway.enums;

public enum ExceptionType {
    UNRECOGNIZED_TRANSACTION("Unrecognized Bank Transaction"),
    UNPAID_TRANSACTION("Unpaid Merchant Transaction"),
    RECONCILIATION_MISMATCH("Reconciliation Mismatch"),
    DUPLICATE_TRANSACTION("Duplicate Transaction"),
    AMOUNT_MISMATCH("Amount Mismatch"),
    INVALID_MERCHANT("Invalid Merchant"),
    SYSTEM_ERROR("System Error");

    private final String displayName;

    ExceptionType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String toString() {
        return displayName;
    }
}

package com.paymentgateway.enums;

public enum TransactionType {
    INCOMING("Incoming Payment"),
    OUTGOING("Outgoing Payment"),
    INTERNAL_TRANSFER("Internal Transfer"),
    PAYOUT("Payout"),
    COMMISSION("Commission"),
    ADJUSTMENT("Adjustment");

    private final String displayName;

    TransactionType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String toString() {
        return displayName;
    }
}

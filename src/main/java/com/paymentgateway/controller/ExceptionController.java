package com.paymentgateway.controller;

import com.paymentgateway.dto.exception.ExceptionResponse;
import com.paymentgateway.dto.exception.ResolveExceptionRequest;
import com.paymentgateway.entity.TransactionException;
import com.paymentgateway.entity.User;
import com.paymentgateway.enums.ExceptionStatus;
import com.paymentgateway.enums.ExceptionType;
import com.paymentgateway.security.CustomUserDetailsService;
import com.paymentgateway.service.ExceptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.UUID;

@RestController
@RequestMapping("/api/exceptions")
@Tag(name = "Exception Management", description = "Transaction exception handling and resolution APIs")
public class ExceptionController {

    @Autowired
    private ExceptionService exceptionService;

    @GetMapping
    @PreAuthorize("hasAuthority('EXCEPTION_VIEW')")
    @Operation(summary = "Get exceptions", description = "Get transaction exceptions with filters and pagination")
    public ResponseEntity<Page<ExceptionResponse>> getExceptions(
            @RequestParam(required = false) ExceptionType type,
            @RequestParam(required = false) ExceptionStatus status,
            @RequestParam(required = false) UUID merchantId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<TransactionException> exceptions = exceptionService.findExceptionsWithFilters(
                type, status, merchantId, startDate, endDate, pageable);

        Page<ExceptionResponse> response = exceptions.map(this::mapToResponse);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{exceptionId}")
    @PreAuthorize("hasAuthority('EXCEPTION_VIEW')")
    @Operation(summary = "Get exception by ID", description = "Get exception details by ID")
    public ResponseEntity<ExceptionResponse> getExceptionById(@PathVariable UUID exceptionId) {
        TransactionException exception = exceptionService.findById(exceptionId)
                .orElseThrow(() -> new IllegalArgumentException("Exception not found: " + exceptionId));

        return ResponseEntity.ok(mapToResponse(exception));
    }

    @GetMapping("/open")
    @PreAuthorize("hasAuthority('EXCEPTION_VIEW')")
    @Operation(summary = "Get open exceptions", description = "Get list of open exceptions")
    public ResponseEntity<Page<ExceptionResponse>> getOpenExceptions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "createdAt"));
        // For now, return empty page - will implement proper pagination in service layer
        Page<ExceptionResponse> response = Page.empty(pageable);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/{exceptionId}/resolve")
    @PreAuthorize("hasAuthority('EXCEPTION_RESOLVE')")
    @Operation(summary = "Resolve exception", description = "Resolve a transaction exception")
    public ResponseEntity<ExceptionResponse> resolveException(
            @PathVariable UUID exceptionId,
            @Valid @RequestBody ResolveExceptionRequest request,
            Authentication authentication) {

        User user = getCurrentUser(authentication);
        TransactionException exception = exceptionService.resolveException(
                exceptionId,
                request.getResolutionNotes(),
                user
        );

        return ResponseEntity.ok(mapToResponse(exception));
    }

    @PostMapping("/{exceptionId}/in-progress")
    @PreAuthorize("hasAuthority('EXCEPTION_RESOLVE')")
    @Operation(summary = "Mark exception as in progress", description = "Mark exception as being worked on")
    public ResponseEntity<ExceptionResponse> markExceptionInProgress(
            @PathVariable UUID exceptionId,
            Authentication authentication) {

        User user = getCurrentUser(authentication);
        TransactionException exception = exceptionService.markExceptionInProgress(exceptionId, user);

        return ResponseEntity.ok(mapToResponse(exception));
    }

    @PostMapping("/{exceptionId}/close")
    @PreAuthorize("hasAuthority('EXCEPTION_RESOLVE')")
    @Operation(summary = "Close exception", description = "Close exception without resolution")
    public ResponseEntity<ExceptionResponse> closeException(
            @PathVariable UUID exceptionId,
            @RequestBody String reason,
            Authentication authentication) {

        User user = getCurrentUser(authentication);
        TransactionException exception = exceptionService.closeException(exceptionId, reason, user);

        return ResponseEntity.ok(mapToResponse(exception));
    }

    @PostMapping("/detect-unpaid")
    @PreAuthorize("hasAuthority('EXCEPTION_RESOLVE')")
    @Operation(summary = "Detect unpaid transactions", description = "Run auto-detection for unpaid transactions")
    public ResponseEntity<String> detectUnpaidTransactions() {
        exceptionService.detectUnpaidTransactions();
        return ResponseEntity.ok("Unpaid transaction detection completed");
    }

    @GetMapping("/stats")
    @PreAuthorize("hasAuthority('EXCEPTION_VIEW')")
    @Operation(summary = "Get exception statistics", description = "Get exception statistics and counts")
    public ResponseEntity<ExceptionStatsResponse> getExceptionStats() {
        ExceptionStatsResponse stats = new ExceptionStatsResponse();
        stats.setOpenCount(exceptionService.countOpenExceptions());
        stats.setUnrecognizedCount(exceptionService.countOpenExceptionsByType(ExceptionType.UNRECOGNIZED_TRANSACTION));
        stats.setUnpaidCount(exceptionService.countOpenExceptionsByType(ExceptionType.UNPAID_TRANSACTION));
        stats.setAmountMismatchCount(exceptionService.countOpenExceptionsByType(ExceptionType.AMOUNT_MISMATCH));
        stats.setReconciliationMismatchCount(exceptionService.countOpenExceptionsByType(ExceptionType.RECONCILIATION_MISMATCH));
        return ResponseEntity.ok(stats);
    }

    private User getCurrentUser(Authentication authentication) {
        if (authentication.getPrincipal() instanceof CustomUserDetailsService.CustomUserPrincipal) {
            return ((CustomUserDetailsService.CustomUserPrincipal) authentication.getPrincipal()).getUser();
        }
        throw new IllegalStateException("Invalid authentication principal");
    }

    private ExceptionResponse mapToResponse(TransactionException exception) {
        ExceptionResponse response = new ExceptionResponse();
        response.setId(exception.getId());
        response.setType(exception.getType());
        response.setStatus(exception.getStatus());
        response.setDescription(exception.getDescription());
        response.setResolutionNotes(exception.getResolutionNotes());
        response.setResolvedAt(exception.getResolvedAt());
        response.setCreatedAt(exception.getCreatedAt());
        response.setUpdatedAt(exception.getUpdatedAt());

        if (exception.getTransaction() != null) {
            response.setTransactionReference(exception.getTransaction().getTransactionReference());
        }

        if (exception.getMerchant() != null) {
            response.setMerchantCode(exception.getMerchant().getMerchantCode());
            response.setMerchantName(exception.getMerchant().getName());
        }

        if (exception.getBankAccount() != null) {
            response.setBankAccountNumber(exception.getBankAccount().getAccountNumber());
        }

        if (exception.getResolvedBy() != null) {
            response.setResolvedByUsername(exception.getResolvedBy().getUsername());
        }

        return response;
    }

    // Simple stats response class
    public static class ExceptionStatsResponse {
        private long openCount;
        private long unrecognizedCount;
        private long unpaidCount;
        private long amountMismatchCount;
        private long reconciliationMismatchCount;

        // Getters and setters
        public long getOpenCount() { return openCount; }
        public void setOpenCount(long openCount) { this.openCount = openCount; }
        public long getUnrecognizedCount() { return unrecognizedCount; }
        public void setUnrecognizedCount(long unrecognizedCount) { this.unrecognizedCount = unrecognizedCount; }
        public long getUnpaidCount() { return unpaidCount; }
        public void setUnpaidCount(long unpaidCount) { this.unpaidCount = unpaidCount; }
        public long getAmountMismatchCount() { return amountMismatchCount; }
        public void setAmountMismatchCount(long amountMismatchCount) { this.amountMismatchCount = amountMismatchCount; }
        public long getReconciliationMismatchCount() { return reconciliationMismatchCount; }
        public void setReconciliationMismatchCount(long reconciliationMismatchCount) { this.reconciliationMismatchCount = reconciliationMismatchCount; }
    }
}

package com.paymentgateway.controller;

import com.paymentgateway.entity.User;
import com.paymentgateway.enums.TransactionStatus;
import com.paymentgateway.enums.TransactionType;
import com.paymentgateway.security.CustomUserDetailsService;
import com.paymentgateway.service.ReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@RestController
@RequestMapping("/api/reports")
@Tag(name = "Report Management", description = "Report generation and export APIs")
public class ReportController {

    @Autowired
    private ReportService reportService;

    @GetMapping("/transactions/export")
    @PreAuthorize("hasAuthority('REPORT_EXPORT')")
    @Operation(summary = "Export transaction report", description = "Export transaction report as CSV")
    public ResponseEntity<byte[]> exportTransactionReport(
            @RequestParam(required = false) UUID merchantId,
            @RequestParam(required = false) UUID agentId,
            @RequestParam(required = false) UUID bankAccountId,
            @RequestParam(required = false) TransactionStatus status,
            @RequestParam(required = false) TransactionType type,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Authentication authentication) throws IOException {

        User user = getCurrentUser(authentication);
        
        // Set default date range if not provided (last 30 days)
        if (startDate == null) {
            startDate = LocalDateTime.now().minusDays(30);
        }
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }

        byte[] csvData = reportService.generateTransactionReport(
                merchantId, agentId, bankAccountId, status, type, startDate, endDate, user);

        String filename = "transactions_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".csv";

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.parseMediaType("text/csv"))
                .body(csvData);
    }

    @GetMapping("/commissions/export")
    @PreAuthorize("hasAuthority('REPORT_EXPORT')")
    @Operation(summary = "Export commission report", description = "Export commission report as CSV")
    public ResponseEntity<byte[]> exportCommissionReport(
            @RequestParam(required = false) UUID merchantId,
            @RequestParam(required = false) UUID agentId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Authentication authentication) throws IOException {

        User user = getCurrentUser(authentication);
        
        // Set default date range if not provided (last 30 days)
        if (startDate == null) {
            startDate = LocalDateTime.now().minusDays(30);
        }
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }

        byte[] csvData = reportService.generateCommissionReport(merchantId, agentId, startDate, endDate, user);

        String filename = "commissions_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".csv";

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.parseMediaType("text/csv"))
                .body(csvData);
    }

    @GetMapping("/merchants/export")
    @PreAuthorize("hasAuthority('REPORT_EXPORT')")
    @Operation(summary = "Export merchant summary report", description = "Export merchant summary report as CSV")
    public ResponseEntity<byte[]> exportMerchantSummaryReport(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Authentication authentication) throws IOException {

        User user = getCurrentUser(authentication);
        
        // Set default date range if not provided (last 30 days)
        if (startDate == null) {
            startDate = LocalDateTime.now().minusDays(30);
        }
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }

        byte[] csvData = reportService.generateMerchantSummaryReport(startDate, endDate, user);

        String filename = "merchant_summary_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".csv";

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.parseMediaType("text/csv"))
                .body(csvData);
    }

    @GetMapping("/agents/export")
    @PreAuthorize("hasAuthority('REPORT_EXPORT')")
    @Operation(summary = "Export agent summary report", description = "Export agent summary report as CSV")
    public ResponseEntity<byte[]> exportAgentSummaryReport(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Authentication authentication) throws IOException {

        User user = getCurrentUser(authentication);
        
        // Set default date range if not provided (last 30 days)
        if (startDate == null) {
            startDate = LocalDateTime.now().minusDays(30);
        }
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }

        byte[] csvData = reportService.generateAgentSummaryReport(startDate, endDate, user);

        String filename = "agent_summary_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".csv";

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.parseMediaType("text/csv"))
                .body(csvData);
    }

    @GetMapping("/exceptions/export")
    @PreAuthorize("hasAuthority('REPORT_EXPORT')")
    @Operation(summary = "Export exception report", description = "Export exception report as CSV")
    public ResponseEntity<byte[]> exportExceptionReport(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Authentication authentication) throws IOException {

        User user = getCurrentUser(authentication);
        
        // Set default date range if not provided (last 30 days)
        if (startDate == null) {
            startDate = LocalDateTime.now().minusDays(30);
        }
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }

        byte[] csvData = reportService.generateExceptionReport(startDate, endDate, user);

        String filename = "exceptions_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".csv";

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.parseMediaType("text/csv"))
                .body(csvData);
    }

    @GetMapping("/dashboard/stats")
    @PreAuthorize("hasAuthority('DASHBOARD_VIEW')")
    @Operation(summary = "Get dashboard statistics", description = "Get dashboard statistics and counts")
    public ResponseEntity<ReportService.DashboardStats> getDashboardStats() {
        ReportService.DashboardStats stats = reportService.getDashboardStats();
        return ResponseEntity.ok(stats);
    }

    private User getCurrentUser(Authentication authentication) {
        if (authentication.getPrincipal() instanceof CustomUserDetailsService.CustomUserPrincipal) {
            return ((CustomUserDetailsService.CustomUserPrincipal) authentication.getPrincipal()).getUser();
        }
        throw new IllegalStateException("Invalid authentication principal");
    }
}

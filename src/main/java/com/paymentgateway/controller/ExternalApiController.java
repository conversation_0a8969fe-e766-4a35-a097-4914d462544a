package com.paymentgateway.controller;

import com.paymentgateway.dto.external.*;
import com.paymentgateway.entity.Merchant;
import com.paymentgateway.entity.Transaction;
import com.paymentgateway.entity.User;
import com.paymentgateway.repository.MerchantRepository;
import com.paymentgateway.service.TransactionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

@RestController
@RequestMapping("/api/external")
@Tag(name = "External API", description = "APIs for external system integrations")
public class ExternalApiController {

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private MerchantRepository merchantRepository;

    @PostMapping("/merchant/transaction")
    @Operation(summary = "Create merchant transaction", 
               description = "External API for merchants to create expected transactions",
               security = @SecurityRequirement(name = "apiKey"))
    public ResponseEntity<ExternalTransactionResponse> createMerchantTransaction(
            @Valid @RequestBody ExternalTransactionRequest request,
            @RequestHeader("X-API-Key") String apiKey,
            HttpServletRequest httpRequest) {

        // Authenticate merchant by API key
        Merchant merchant = authenticateMerchant(apiKey);
        
        // Create system user for external API
        User systemUser = createSystemUser("EXTERNAL_API", httpRequest.getRemoteAddr());

        try {
            Transaction transaction = transactionService.createExpectedTransaction(
                    merchant.getMerchantCode(),
                    request.getExternalReference(),
                    request.getAmount(),
                    request.getDescription(),
                    systemUser
            );

            ExternalTransactionResponse response = new ExternalTransactionResponse();
            response.setSuccess(true);
            response.setTransactionReference(transaction.getTransactionReference());
            response.setStatus(transaction.getStatus().name());
            response.setMessage("Transaction created successfully");
            response.setTimestamp(LocalDateTime.now());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            ExternalTransactionResponse response = new ExternalTransactionResponse();
            response.setSuccess(false);
            response.setMessage("Failed to create transaction: " + e.getMessage());
            response.setTimestamp(LocalDateTime.now());
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/merchant/transaction/{transactionReference}")
    @Operation(summary = "Get transaction status", 
               description = "Get transaction status by reference",
               security = @SecurityRequirement(name = "apiKey"))
    public ResponseEntity<ExternalTransactionStatusResponse> getTransactionStatus(
            @PathVariable String transactionReference,
            @RequestHeader("X-API-Key") String apiKey) {

        // Authenticate merchant
        Merchant merchant = authenticateMerchant(apiKey);

        try {
            Transaction transaction = transactionService.findByTransactionReference(transactionReference)
                    .orElseThrow(() -> new IllegalArgumentException("Transaction not found"));

            // Verify transaction belongs to this merchant
            if (!transaction.getMerchant().getId().equals(merchant.getId())) {
                throw new IllegalArgumentException("Transaction not found");
            }

            ExternalTransactionStatusResponse response = new ExternalTransactionStatusResponse();
            response.setSuccess(true);
            response.setTransactionReference(transaction.getTransactionReference());
            response.setExternalReference(transaction.getExternalReference());
            response.setStatus(transaction.getStatus().name());
            response.setAmount(transaction.getAmount());
            response.setNetAmount(transaction.getNetAmount());
            response.setCommissionAmount(transaction.getCommissionAmount());
            response.setTransactionDate(transaction.getTransactionDate());
            response.setIsMatched(transaction.isMatched());
            response.setTimestamp(LocalDateTime.now());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            ExternalTransactionStatusResponse response = new ExternalTransactionStatusResponse();
            response.setSuccess(false);
            response.setMessage("Failed to get transaction status: " + e.getMessage());
            response.setTimestamp(LocalDateTime.now());
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/bank/transaction")
    @Operation(summary = "Process bank transaction", 
               description = "External API for banks to report incoming transactions",
               security = @SecurityRequirement(name = "apiKey"))
    public ResponseEntity<ExternalBankTransactionResponse> processBankTransaction(
            @Valid @RequestBody ExternalBankTransactionRequest request,
            @RequestHeader("X-API-Key") String apiKey,
            HttpServletRequest httpRequest) {

        // For bank API, we could use a different authentication mechanism
        // For now, using a system API key validation
        validateSystemApiKey(apiKey);
        
        User systemUser = createSystemUser("BANK_API", httpRequest.getRemoteAddr());

        try {
            Transaction transaction = transactionService.processBankTransaction(
                    request.getBankAccountNumber(),
                    request.getAmount(),
                    request.getBankReference(),
                    request.getTransactionDate(),
                    request.getDescription(),
                    systemUser
            );

            ExternalBankTransactionResponse response = new ExternalBankTransactionResponse();
            response.setSuccess(true);
            response.setTransactionReference(transaction.getTransactionReference());
            response.setBankReference(request.getBankReference());
            response.setStatus(transaction.getStatus().name());
            response.setMessage("Bank transaction processed successfully");
            response.setTimestamp(LocalDateTime.now());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            ExternalBankTransactionResponse response = new ExternalBankTransactionResponse();
            response.setSuccess(false);
            response.setMessage("Failed to process bank transaction: " + e.getMessage());
            response.setTimestamp(LocalDateTime.now());
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/merchant/balance")
    @Operation(summary = "Get merchant balance", 
               description = "Get current merchant balance",
               security = @SecurityRequirement(name = "apiKey"))
    public ResponseEntity<ExternalBalanceResponse> getMerchantBalance(
            @RequestHeader("X-API-Key") String apiKey) {

        try {
            Merchant merchant = authenticateMerchant(apiKey);

            ExternalBalanceResponse response = new ExternalBalanceResponse();
            response.setSuccess(true);
            response.setMerchantCode(merchant.getMerchantCode());
            response.setCurrentBalance(merchant.getCurrentBalance());
            response.setTimestamp(LocalDateTime.now());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            ExternalBalanceResponse response = new ExternalBalanceResponse();
            response.setSuccess(false);
            response.setMessage("Failed to get balance: " + e.getMessage());
            response.setTimestamp(LocalDateTime.now());
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/webhook/test")
    @Operation(summary = "Test webhook endpoint", 
               description = "Test endpoint for webhook functionality")
    public ResponseEntity<String> testWebhook(@RequestBody String payload) {
        // This endpoint can be used to test webhook functionality
        // In a real implementation, this would trigger webhook calls to merchants
        return ResponseEntity.ok("Webhook received: " + payload);
    }

    private Merchant authenticateMerchant(String apiKey) {
        return merchantRepository.findByApiKey(apiKey)
                .filter(Merchant::getIsActive)
                .orElseThrow(() -> new IllegalArgumentException("Invalid API key"));
    }

    private void validateSystemApiKey(String apiKey) {
        // In a real implementation, this would validate against a system API key
        // For now, we'll use a simple validation
        if (!"SYSTEM_BANK_API_KEY".equals(apiKey)) {
            throw new IllegalArgumentException("Invalid system API key");
        }
    }

    private User createSystemUser(String source, String ipAddress) {
        User systemUser = new User();
        systemUser.setUsername("system_" + source.toLowerCase());
        systemUser.setEmail("<EMAIL>");
        systemUser.setFirstName("System");
        systemUser.setLastName(source);
        return systemUser;
    }
}

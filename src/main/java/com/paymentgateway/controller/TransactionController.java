package com.paymentgateway.controller;

import com.paymentgateway.dto.transaction.*;
import com.paymentgateway.entity.Transaction;
import com.paymentgateway.entity.User;
import com.paymentgateway.enums.TransactionStatus;
import com.paymentgateway.enums.TransactionType;
import com.paymentgateway.security.CustomUserDetailsService;
import com.paymentgateway.service.TransactionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.UUID;

@RestController
@RequestMapping("/api/transactions")
@Tag(name = "Transaction Management", description = "Transaction processing and management APIs")
public class TransactionController {

    @Autowired
    private TransactionService transactionService;

    @PostMapping("/expected")
    @PreAuthorize("hasAuthority('TRANSACTION_CREATE')")
    @Operation(summary = "Create expected transaction", description = "Create an expected transaction from merchant")
    public ResponseEntity<TransactionResponse> createExpectedTransaction(
            @Valid @RequestBody CreateTransactionRequest request,
            Authentication authentication) {
        
        User user = getCurrentUser(authentication);
        Transaction transaction = transactionService.createExpectedTransaction(
                request.getMerchantCode(),
                request.getExternalReference(),
                request.getAmount(),
                request.getDescription(),
                user
        );

        return ResponseEntity.ok(mapToResponse(transaction));
    }

    @PostMapping("/bank")
    @PreAuthorize("hasAuthority('TRANSACTION_CREATE')")
    @Operation(summary = "Process bank transaction", description = "Process incoming bank transaction")
    public ResponseEntity<TransactionResponse> processBankTransaction(
            @Valid @RequestBody BankTransactionRequest request,
            Authentication authentication) {
        
        User user = getCurrentUser(authentication);
        Transaction transaction = transactionService.processBankTransaction(
                request.getBankAccountNumber(),
                request.getAmount(),
                request.getBankReference(),
                request.getTransactionDate(),
                request.getDescription(),
                user
        );

        return ResponseEntity.ok(mapToResponse(transaction));
    }

    @PostMapping("/{transactionId}/match")
    @PreAuthorize("hasAuthority('TRANSACTION_UPDATE')")
    @Operation(summary = "Manual transaction matching", description = "Manually match a bank transaction")
    public ResponseEntity<String> matchTransaction(@PathVariable UUID transactionId) {
        Transaction transaction = transactionService.findByTransactionReference(transactionId.toString())
                .orElseThrow(() -> new IllegalArgumentException("Transaction not found"));

        transactionService.matchTransaction(transaction);
        return ResponseEntity.ok("Transaction matching initiated");
    }

    @GetMapping
    @PreAuthorize("hasAuthority('TRANSACTION_VIEW')")
    @Operation(summary = "Get transactions", description = "Get transactions with filters and pagination")
    public ResponseEntity<Page<TransactionResponse>> getTransactions(
            @RequestParam(required = false) UUID merchantId,
            @RequestParam(required = false) UUID agentId,
            @RequestParam(required = false) UUID bankAccountId,
            @RequestParam(required = false) TransactionStatus status,
            @RequestParam(required = false) TransactionType type,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "transactionDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<Transaction> transactions = transactionService.findTransactionsWithFilters(
                merchantId, agentId, bankAccountId, status, type, startDate, endDate, pageable);

        Page<TransactionResponse> response = transactions.map(this::mapToResponse);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{transactionReference}")
    @PreAuthorize("hasAuthority('TRANSACTION_VIEW')")
    @Operation(summary = "Get transaction by reference", description = "Get transaction details by reference")
    public ResponseEntity<TransactionResponse> getTransactionByReference(@PathVariable String transactionReference) {
        Transaction transaction = transactionService.findByTransactionReference(transactionReference)
                .orElseThrow(() -> new IllegalArgumentException("Transaction not found: " + transactionReference));

        return ResponseEntity.ok(mapToResponse(transaction));
    }

    @GetMapping("/unpaid")
    @PreAuthorize("hasAuthority('TRANSACTION_VIEW')")
    @Operation(summary = "Get unpaid transactions", description = "Get list of unpaid merchant transactions")
    public ResponseEntity<Page<TransactionResponse>> getUnpaidTransactions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "transactionDate"));
        // For now, return empty page - will implement proper pagination in service layer
        Page<TransactionResponse> response = Page.empty(pageable);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/unrecognized")
    @PreAuthorize("hasAuthority('TRANSACTION_VIEW')")
    @Operation(summary = "Get unrecognized transactions", description = "Get list of unrecognized bank transactions")
    public ResponseEntity<Page<TransactionResponse>> getUnrecognizedTransactions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "transactionDate"));
        // For now, return empty page - will implement proper pagination in service layer
        Page<TransactionResponse> response = Page.empty(pageable);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/stats")
    @PreAuthorize("hasAuthority('TRANSACTION_VIEW')")
    @Operation(summary = "Get transaction statistics", description = "Get transaction statistics and counts")
    public ResponseEntity<TransactionStatsResponse> getTransactionStats() {
        // Implementation will be added in next phase
        TransactionStatsResponse stats = new TransactionStatsResponse();
        return ResponseEntity.ok(stats);
    }

    private User getCurrentUser(Authentication authentication) {
        if (authentication.getPrincipal() instanceof CustomUserDetailsService.CustomUserPrincipal) {
            return ((CustomUserDetailsService.CustomUserPrincipal) authentication.getPrincipal()).getUser();
        }
        throw new IllegalStateException("Invalid authentication principal");
    }

    private TransactionResponse mapToResponse(Transaction transaction) {
        TransactionResponse response = new TransactionResponse();
        response.setId(transaction.getId());
        response.setTransactionReference(transaction.getTransactionReference());
        response.setExternalReference(transaction.getExternalReference());
        response.setType(transaction.getType());
        response.setStatus(transaction.getStatus());
        response.setAmount(transaction.getAmount());
        response.setCommissionAmount(transaction.getCommissionAmount());
        response.setNetAmount(transaction.getNetAmount());
        response.setTransactionDate(transaction.getTransactionDate());
        response.setDescription(transaction.getDescription());
        response.setIsExpected(transaction.getIsExpected());
        response.setIsBankConfirmed(transaction.getIsBankConfirmed());
        response.setMatchedAt(transaction.getMatchedAt());
        response.setCreatedAt(transaction.getCreatedAt());
        response.setUpdatedAt(transaction.getUpdatedAt());

        if (transaction.getMerchant() != null) {
            response.setMerchantCode(transaction.getMerchant().getMerchantCode());
            response.setMerchantName(transaction.getMerchant().getName());
        }

        if (transaction.getAgent() != null) {
            response.setAgentCode(transaction.getAgent().getAgentCode());
            response.setAgentName(transaction.getAgent().getName());
        }

        if (transaction.getBankAccount() != null) {
            response.setBankAccountNumber(transaction.getBankAccount().getAccountNumber());
            response.setBankAccountName(transaction.getBankAccount().getAccountName());
        }

        return response;
    }

    // Simple stats response class
    public static class TransactionStatsResponse {
        private long totalTransactions = 0;
        private long pendingTransactions = 0;
        private long matchedTransactions = 0;
        private long exceptionTransactions = 0;

        // Getters and setters
        public long getTotalTransactions() { return totalTransactions; }
        public void setTotalTransactions(long totalTransactions) { this.totalTransactions = totalTransactions; }
        public long getPendingTransactions() { return pendingTransactions; }
        public void setPendingTransactions(long pendingTransactions) { this.pendingTransactions = pendingTransactions; }
        public long getMatchedTransactions() { return matchedTransactions; }
        public void setMatchedTransactions(long matchedTransactions) { this.matchedTransactions = matchedTransactions; }
        public long getExceptionTransactions() { return exceptionTransactions; }
        public void setExceptionTransactions(long exceptionTransactions) { this.exceptionTransactions = exceptionTransactions; }
    }
}

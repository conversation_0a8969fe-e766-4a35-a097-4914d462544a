package com.paymentgateway.controller;

import com.paymentgateway.dto.payout.*;
import com.paymentgateway.entity.PayoutRequest;
import com.paymentgateway.entity.User;
import com.paymentgateway.enums.PayoutStatus;
import com.paymentgateway.security.CustomUserDetailsService;
import com.paymentgateway.service.PayoutService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.UUID;

@RestController
@RequestMapping("/api/payouts")
@Tag(name = "Payout Management", description = "Payout request processing and management APIs")
public class PayoutController {

    @Autowired
    private PayoutService payoutService;

    @PostMapping
    @PreAuthorize("hasAuthority('PAYOUT_VIEW')")
    @Operation(summary = "Create payout request", description = "Create a new payout request")
    public ResponseEntity<PayoutResponse> createPayoutRequest(
            @Valid @RequestBody CreatePayoutRequest request,
            Authentication authentication) {

        User user = getCurrentUser(authentication);
        PayoutRequest payoutRequest = payoutService.createPayoutRequest(
                request.getMerchantCode(),
                request.getAmount(),
                request.getNotes(),
                user
        );

        return ResponseEntity.ok(mapToResponse(payoutRequest));
    }

    @PostMapping("/{payoutId}/process")
    @PreAuthorize("hasAuthority('PAYOUT_PROCESS')")
    @Operation(summary = "Process payout request", description = "Process and approve a payout request")
    public ResponseEntity<PayoutResponse> processPayoutRequest(
            @PathVariable UUID payoutId,
            @Valid @RequestBody ProcessPayoutRequest request,
            Authentication authentication) {

        User user = getCurrentUser(authentication);
        PayoutRequest payoutRequest = payoutService.processPayoutRequest(
                payoutId,
                request.getProcessingNotes(),
                request.getBankAccountId(),
                user
        );

        return ResponseEntity.ok(mapToResponse(payoutRequest));
    }

    @PostMapping("/{payoutId}/confirm")
    @PreAuthorize("hasAuthority('PAYOUT_CONFIRM')")
    @Operation(summary = "Confirm payout request", description = "Confirm completion of a payout request")
    public ResponseEntity<PayoutResponse> confirmPayoutRequest(
            @PathVariable UUID payoutId,
            @Valid @RequestBody ConfirmPayoutRequest request,
            Authentication authentication) {

        User user = getCurrentUser(authentication);
        PayoutRequest payoutRequest = payoutService.confirmPayoutRequest(
                payoutId,
                request.getExternalReference(),
                user
        );

        return ResponseEntity.ok(mapToResponse(payoutRequest));
    }

    @PostMapping("/{payoutId}/reject")
    @PreAuthorize("hasAuthority('PAYOUT_PROCESS')")
    @Operation(summary = "Reject payout request", description = "Reject a payout request")
    public ResponseEntity<PayoutResponse> rejectPayoutRequest(
            @PathVariable UUID payoutId,
            @RequestBody String rejectionReason,
            Authentication authentication) {

        User user = getCurrentUser(authentication);
        PayoutRequest payoutRequest = payoutService.rejectPayoutRequest(
                payoutId,
                rejectionReason,
                user
        );

        return ResponseEntity.ok(mapToResponse(payoutRequest));
    }

    @PostMapping("/{payoutId}/cancel")
    @PreAuthorize("hasAuthority('PAYOUT_VIEW')")
    @Operation(summary = "Cancel payout request", description = "Cancel a pending payout request")
    public ResponseEntity<PayoutResponse> cancelPayoutRequest(
            @PathVariable UUID payoutId,
            Authentication authentication) {

        User user = getCurrentUser(authentication);
        PayoutRequest payoutRequest = payoutService.cancelPayoutRequest(payoutId, user);

        return ResponseEntity.ok(mapToResponse(payoutRequest));
    }

    @GetMapping
    @PreAuthorize("hasAuthority('PAYOUT_VIEW')")
    @Operation(summary = "Get payout requests", description = "Get payout requests with filters and pagination")
    public ResponseEntity<Page<PayoutResponse>> getPayoutRequests(
            @RequestParam(required = false) UUID merchantId,
            @RequestParam(required = false) PayoutStatus status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "requestedAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<PayoutRequest> payoutRequests = payoutService.findPayoutRequestsWithFilters(
                merchantId, status, startDate, endDate, pageable);

        Page<PayoutResponse> response = payoutRequests.map(this::mapToResponse);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{payoutId}")
    @PreAuthorize("hasAuthority('PAYOUT_VIEW')")
    @Operation(summary = "Get payout request by ID", description = "Get payout request details by ID")
    public ResponseEntity<PayoutResponse> getPayoutRequestById(@PathVariable UUID payoutId) {
        PayoutRequest payoutRequest = payoutService.findById(payoutId)
                .orElseThrow(() -> new IllegalArgumentException("Payout request not found: " + payoutId));

        return ResponseEntity.ok(mapToResponse(payoutRequest));
    }

    @GetMapping("/pending")
    @PreAuthorize("hasAuthority('PAYOUT_VIEW')")
    @Operation(summary = "Get pending payout requests", description = "Get list of pending payout requests")
    public ResponseEntity<Page<PayoutResponse>> getPendingPayoutRequests(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.ASC, "requestedAt"));
        // For now, return empty page - will implement proper pagination in service layer
        Page<PayoutResponse> response = Page.empty(pageable);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/stats")
    @PreAuthorize("hasAuthority('PAYOUT_VIEW')")
    @Operation(summary = "Get payout statistics", description = "Get payout statistics and counts")
    public ResponseEntity<PayoutStatsResponse> getPayoutStats() {
        PayoutStatsResponse stats = new PayoutStatsResponse();
        stats.setPendingCount(payoutService.countPendingRequests());
        return ResponseEntity.ok(stats);
    }

    private User getCurrentUser(Authentication authentication) {
        if (authentication.getPrincipal() instanceof CustomUserDetailsService.CustomUserPrincipal) {
            return ((CustomUserDetailsService.CustomUserPrincipal) authentication.getPrincipal()).getUser();
        }
        throw new IllegalStateException("Invalid authentication principal");
    }

    private PayoutResponse mapToResponse(PayoutRequest payoutRequest) {
        PayoutResponse response = new PayoutResponse();
        response.setId(payoutRequest.getId());
        response.setAmount(payoutRequest.getAmount());
        response.setStatus(payoutRequest.getStatus());
        response.setRequestedAt(payoutRequest.getRequestedAt());
        response.setProcessedAt(payoutRequest.getProcessedAt());
        response.setConfirmedAt(payoutRequest.getConfirmedAt());
        response.setNotes(payoutRequest.getNotes());
        response.setProcessingNotes(payoutRequest.getProcessingNotes());
        response.setExternalReference(payoutRequest.getExternalReference());
        response.setCreatedAt(payoutRequest.getCreatedAt());
        response.setUpdatedAt(payoutRequest.getUpdatedAt());

        if (payoutRequest.getMerchant() != null) {
            response.setMerchantCode(payoutRequest.getMerchant().getMerchantCode());
            response.setMerchantName(payoutRequest.getMerchant().getName());
        }

        if (payoutRequest.getProcessedBy() != null) {
            response.setProcessedByUsername(payoutRequest.getProcessedBy().getUsername());
        }

        if (payoutRequest.getConfirmedBy() != null) {
            response.setConfirmedByUsername(payoutRequest.getConfirmedBy().getUsername());
        }

        if (payoutRequest.getBankAccount() != null) {
            response.setBankAccountNumber(payoutRequest.getBankAccount().getAccountNumber());
            response.setBankAccountName(payoutRequest.getBankAccount().getAccountName());
        }

        return response;
    }

    // Simple stats response class
    public static class PayoutStatsResponse {
        private long pendingCount;
        private long processingCount;
        private long completedCount;
        private long rejectedCount;

        // Getters and setters
        public long getPendingCount() { return pendingCount; }
        public void setPendingCount(long pendingCount) { this.pendingCount = pendingCount; }
        public long getProcessingCount() { return processingCount; }
        public void setProcessingCount(long processingCount) { this.processingCount = processingCount; }
        public long getCompletedCount() { return completedCount; }
        public void setCompletedCount(long completedCount) { this.completedCount = completedCount; }
        public long getRejectedCount() { return rejectedCount; }
        public void setRejectedCount(long rejectedCount) { this.rejectedCount = rejectedCount; }
    }
}

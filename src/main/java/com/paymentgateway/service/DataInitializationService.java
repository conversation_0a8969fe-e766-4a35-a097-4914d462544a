package com.paymentgateway.service;

import com.paymentgateway.entity.Permission;
import com.paymentgateway.entity.Role;
import com.paymentgateway.entity.User;
import com.paymentgateway.repository.PermissionRepository;
import com.paymentgateway.repository.RoleRepository;
import com.paymentgateway.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Service
@Transactional
public class DataInitializationService implements CommandLineRunner {

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        initializePermissions();
        initializeRoles();
        initializeDefaultAdmin();
    }

    private void initializePermissions() {
        List<Permission> permissions = Arrays.asList(
            // User Management
            new Permission("USER_VIEW", "USER", "VIEW", "View users"),
            new Permission("USER_CREATE", "USER", "CREATE", "Create users"),
            new Permission("USER_UPDATE", "USER", "UPDATE", "Update users"),
            new Permission("USER_DELETE", "USER", "DELETE", "Delete users"),
            
            // Role Management
            new Permission("ROLE_VIEW", "ROLE", "VIEW", "View roles"),
            new Permission("ROLE_CREATE", "ROLE", "CREATE", "Create roles"),
            new Permission("ROLE_UPDATE", "ROLE", "UPDATE", "Update roles"),
            new Permission("ROLE_DELETE", "ROLE", "DELETE", "Delete roles"),
            
            // Bank Account Management
            new Permission("BANK_ACCOUNT_VIEW", "BANK_ACCOUNT", "VIEW", "View bank accounts"),
            new Permission("BANK_ACCOUNT_CREATE", "BANK_ACCOUNT", "CREATE", "Create bank accounts"),
            new Permission("BANK_ACCOUNT_UPDATE", "BANK_ACCOUNT", "UPDATE", "Update bank accounts"),
            new Permission("BANK_ACCOUNT_DELETE", "BANK_ACCOUNT", "DELETE", "Delete bank accounts"),
            
            // Merchant Management
            new Permission("MERCHANT_VIEW", "MERCHANT", "VIEW", "View merchants"),
            new Permission("MERCHANT_CREATE", "MERCHANT", "CREATE", "Create merchants"),
            new Permission("MERCHANT_UPDATE", "MERCHANT", "UPDATE", "Update merchants"),
            new Permission("MERCHANT_DELETE", "MERCHANT", "DELETE", "Delete merchants"),
            
            // Agent Management
            new Permission("AGENT_VIEW", "AGENT", "VIEW", "View agents"),
            new Permission("AGENT_CREATE", "AGENT", "CREATE", "Create agents"),
            new Permission("AGENT_UPDATE", "AGENT", "UPDATE", "Update agents"),
            new Permission("AGENT_DELETE", "AGENT", "DELETE", "Delete agents"),
            
            // Transaction Management
            new Permission("TRANSACTION_VIEW", "TRANSACTION", "VIEW", "View transactions"),
            new Permission("TRANSACTION_CREATE", "TRANSACTION", "CREATE", "Create transactions"),
            new Permission("TRANSACTION_UPDATE", "TRANSACTION", "UPDATE", "Update transactions"),
            new Permission("TRANSACTION_DELETE", "TRANSACTION", "DELETE", "Delete transactions"),
            
            // Exception Management
            new Permission("EXCEPTION_VIEW", "EXCEPTION", "VIEW", "View exceptions"),
            new Permission("EXCEPTION_RESOLVE", "EXCEPTION", "RESOLVE", "Resolve exceptions"),
            
            // Payout Management
            new Permission("PAYOUT_VIEW", "PAYOUT", "VIEW", "View payouts"),
            new Permission("PAYOUT_PROCESS", "PAYOUT", "PROCESS", "Process payouts"),
            new Permission("PAYOUT_CONFIRM", "PAYOUT", "CONFIRM", "Confirm payouts"),
            
            // Report Management
            new Permission("REPORT_VIEW", "REPORT", "VIEW", "View reports"),
            new Permission("REPORT_EXPORT", "REPORT", "EXPORT", "Export reports"),
            
            // Dashboard
            new Permission("DASHBOARD_VIEW", "DASHBOARD", "VIEW", "View dashboard")
        );

        for (Permission permission : permissions) {
            if (!permissionRepository.existsByName(permission.getName())) {
                permissionRepository.save(permission);
            }
        }
    }

    private void initializeRoles() {
        // Admin Role - Full access
        if (!roleRepository.existsByName("ADMIN")) {
            Role adminRole = new Role("ADMIN", "System Administrator");
            List<Permission> allPermissions = permissionRepository.findByIsActiveTrue();
            adminRole.setPermissions(allPermissions.stream().collect(java.util.stream.Collectors.toSet()));
            roleRepository.save(adminRole);
        }

        // Operator Role - Operations access
        if (!roleRepository.existsByName("OPERATOR")) {
            Role operatorRole = new Role("OPERATOR", "System Operator");
            List<String> operatorPermissions = Arrays.asList(
                "DASHBOARD_VIEW", "BANK_ACCOUNT_VIEW", "BANK_ACCOUNT_CREATE", "BANK_ACCOUNT_UPDATE",
                "MERCHANT_VIEW", "MERCHANT_CREATE", "MERCHANT_UPDATE",
                "AGENT_VIEW", "AGENT_CREATE", "AGENT_UPDATE",
                "TRANSACTION_VIEW", "TRANSACTION_CREATE", "TRANSACTION_UPDATE",
                "EXCEPTION_VIEW", "EXCEPTION_RESOLVE",
                "PAYOUT_VIEW", "PAYOUT_PROCESS", "PAYOUT_CONFIRM",
                "REPORT_VIEW", "REPORT_EXPORT"
            );
            
            for (String permName : operatorPermissions) {
                permissionRepository.findByName(permName).ifPresent(operatorRole::addPermission);
            }
            roleRepository.save(operatorRole);
        }

        // Merchant Role - Limited merchant access
        if (!roleRepository.existsByName("MERCHANT")) {
            Role merchantRole = new Role("MERCHANT", "Merchant User");
            List<String> merchantPermissions = Arrays.asList(
                "DASHBOARD_VIEW", "TRANSACTION_VIEW", "PAYOUT_VIEW", "REPORT_VIEW"
            );
            
            for (String permName : merchantPermissions) {
                permissionRepository.findByName(permName).ifPresent(merchantRole::addPermission);
            }
            roleRepository.save(merchantRole);
        }

        // Agent Role - Agent access
        if (!roleRepository.existsByName("AGENT")) {
            Role agentRole = new Role("AGENT", "Agent User");
            List<String> agentPermissions = Arrays.asList(
                "DASHBOARD_VIEW", "MERCHANT_VIEW", "TRANSACTION_VIEW", "REPORT_VIEW"
            );
            
            for (String permName : agentPermissions) {
                permissionRepository.findByName(permName).ifPresent(agentRole::addPermission);
            }
            roleRepository.save(agentRole);
        }
    }

    private void initializeDefaultAdmin() {
        if (!userRepository.existsByUsername("admin")) {
            User admin = new User();
            admin.setUsername("admin");
            admin.setEmail("<EMAIL>");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setFirstName("System");
            admin.setLastName("Administrator");
            admin.setIsActive(true);
            admin.setPasswordChangedAt(LocalDateTime.now());
            
            roleRepository.findByName("ADMIN").ifPresent(admin::addRole);
            userRepository.save(admin);
        }
    }
}

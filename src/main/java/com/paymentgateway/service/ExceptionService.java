package com.paymentgateway.service;

import com.paymentgateway.entity.*;
import com.paymentgateway.enums.ExceptionStatus;
import com.paymentgateway.enums.ExceptionType;
import com.paymentgateway.enums.TransactionStatus;
import com.paymentgateway.repository.TransactionExceptionRepository;
import com.paymentgateway.repository.TransactionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class ExceptionService {

    @Autowired
    private TransactionExceptionRepository exceptionRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private AuditService auditService;

    /**
     * Create exception for unrecognized bank transaction
     */
    public TransactionException createUnrecognizedTransactionException(Transaction bankTransaction) {
        String description = String.format("Unrecognized bank transaction: %s, Amount: %s, Date: %s",
                bankTransaction.getTransactionReference(),
                bankTransaction.getAmount(),
                bankTransaction.getTransactionDate());

        TransactionException exception = new TransactionException(ExceptionType.UNRECOGNIZED_TRANSACTION, description);
        exception.setTransaction(bankTransaction);
        exception.setBankAccount(bankTransaction.getBankAccount());

        exception = exceptionRepository.save(exception);

        // Update transaction status
        bankTransaction.setStatus(TransactionStatus.EXCEPTION);
        transactionRepository.save(bankTransaction);

        auditService.logSystemAction("EXCEPTION_CREATED", "TransactionException", exception.getId(),
                "Created unrecognized transaction exception for: " + bankTransaction.getTransactionReference());

        return exception;
    }

    /**
     * Create exception for unpaid merchant transaction
     */
    public TransactionException createUnpaidTransactionException(Transaction merchantTransaction) {
        String description = String.format("Unpaid merchant transaction: %s, Amount: %s, Expected Date: %s",
                merchantTransaction.getTransactionReference(),
                merchantTransaction.getAmount(),
                merchantTransaction.getTransactionDate());

        TransactionException exception = new TransactionException(ExceptionType.UNPAID_TRANSACTION, description);
        exception.setTransaction(merchantTransaction);
        exception.setMerchant(merchantTransaction.getMerchant());

        exception = exceptionRepository.save(exception);

        // Update transaction status
        merchantTransaction.setStatus(TransactionStatus.EXCEPTION);
        transactionRepository.save(merchantTransaction);

        auditService.logSystemAction("EXCEPTION_CREATED", "TransactionException", exception.getId(),
                "Created unpaid transaction exception for: " + merchantTransaction.getTransactionReference());

        return exception;
    }

    /**
     * Create exception for amount mismatch
     */
    public TransactionException createAmountMismatchException(Transaction bankTransaction, Transaction expectedTransaction) {
        String description = String.format("Amount mismatch: Bank transaction %s (Amount: %s) vs Expected transaction %s (Amount: %s)",
                bankTransaction.getTransactionReference(),
                bankTransaction.getAmount(),
                expectedTransaction.getTransactionReference(),
                expectedTransaction.getAmount());

        TransactionException exception = new TransactionException(ExceptionType.AMOUNT_MISMATCH, description);
        exception.setTransaction(bankTransaction);
        exception.setMerchant(expectedTransaction.getMerchant());
        exception.setBankAccount(bankTransaction.getBankAccount());

        exception = exceptionRepository.save(exception);

        // Update both transaction statuses
        bankTransaction.setStatus(TransactionStatus.EXCEPTION);
        expectedTransaction.setStatus(TransactionStatus.EXCEPTION);
        transactionRepository.save(bankTransaction);
        transactionRepository.save(expectedTransaction);

        auditService.logSystemAction("EXCEPTION_CREATED", "TransactionException", exception.getId(),
                "Created amount mismatch exception between: " + bankTransaction.getTransactionReference() + 
                " and " + expectedTransaction.getTransactionReference());

        return exception;
    }

    /**
     * Create exception for multiple transaction matches
     */
    public TransactionException createMultipleMatchException(Transaction bankTransaction, List<Transaction> expectedTransactions) {
        StringBuilder description = new StringBuilder();
        description.append(String.format("Multiple matches found for bank transaction %s (Amount: %s). Possible matches: ",
                bankTransaction.getTransactionReference(), bankTransaction.getAmount()));

        for (Transaction expected : expectedTransactions) {
            description.append(expected.getTransactionReference()).append(" ");
        }

        TransactionException exception = new TransactionException(ExceptionType.RECONCILIATION_MISMATCH, description.toString());
        exception.setTransaction(bankTransaction);
        exception.setBankAccount(bankTransaction.getBankAccount());

        exception = exceptionRepository.save(exception);

        // Update transaction status
        bankTransaction.setStatus(TransactionStatus.EXCEPTION);
        transactionRepository.save(bankTransaction);

        auditService.logSystemAction("EXCEPTION_CREATED", "TransactionException", exception.getId(),
                "Created multiple match exception for: " + bankTransaction.getTransactionReference());

        return exception;
    }

    /**
     * Create exception for duplicate transaction
     */
    public TransactionException createDuplicateTransactionException(Transaction transaction, String duplicateReference) {
        String description = String.format("Duplicate transaction detected: %s conflicts with existing reference: %s",
                transaction.getTransactionReference(), duplicateReference);

        TransactionException exception = new TransactionException(ExceptionType.DUPLICATE_TRANSACTION, description);
        exception.setTransaction(transaction);
        exception.setMerchant(transaction.getMerchant());

        exception = exceptionRepository.save(exception);

        auditService.logSystemAction("EXCEPTION_CREATED", "TransactionException", exception.getId(),
                "Created duplicate transaction exception for: " + transaction.getTransactionReference());

        return exception;
    }

    /**
     * Create system error exception
     */
    public TransactionException createSystemErrorException(Transaction transaction, String errorMessage) {
        String description = String.format("System error processing transaction %s: %s",
                transaction.getTransactionReference(), errorMessage);

        TransactionException exception = new TransactionException(ExceptionType.SYSTEM_ERROR, description);
        exception.setTransaction(transaction);
        exception.setMerchant(transaction.getMerchant());

        exception = exceptionRepository.save(exception);

        // Update transaction status
        transaction.setStatus(TransactionStatus.EXCEPTION);
        transactionRepository.save(transaction);

        auditService.logSystemAction("EXCEPTION_CREATED", "TransactionException", exception.getId(),
                "Created system error exception for: " + transaction.getTransactionReference());

        return exception;
    }

    /**
     * Resolve an exception
     */
    public TransactionException resolveException(UUID exceptionId, String resolutionNotes, User resolvedBy) {
        TransactionException exception = exceptionRepository.findById(exceptionId)
                .orElseThrow(() -> new IllegalArgumentException("Exception not found: " + exceptionId));

        if (exception.getStatus() != ExceptionStatus.OPEN && exception.getStatus() != ExceptionStatus.IN_PROGRESS) {
            throw new IllegalStateException("Exception is already resolved: " + exceptionId);
        }

        exception.resolve(resolvedBy, resolutionNotes);
        exception = exceptionRepository.save(exception);

        // Update related transaction status if applicable
        if (exception.getTransaction() != null) {
            Transaction transaction = exception.getTransaction();
            if (transaction.getStatus() == TransactionStatus.EXCEPTION) {
                transaction.setStatus(TransactionStatus.COMPLETED);
                transactionRepository.save(transaction);
            }
        }

        auditService.logUserAction(resolvedBy, "EXCEPTION_RESOLVED", "TransactionException", exception.getId(),
                "Resolved exception: " + resolutionNotes);

        return exception;
    }

    /**
     * Mark exception as in progress
     */
    public TransactionException markExceptionInProgress(UUID exceptionId, User user) {
        TransactionException exception = exceptionRepository.findById(exceptionId)
                .orElseThrow(() -> new IllegalArgumentException("Exception not found: " + exceptionId));

        if (exception.getStatus() != ExceptionStatus.OPEN) {
            throw new IllegalStateException("Exception is not open: " + exceptionId);
        }

        exception.setStatus(ExceptionStatus.IN_PROGRESS);
        exception = exceptionRepository.save(exception);

        auditService.logUserAction(user, "EXCEPTION_IN_PROGRESS", "TransactionException", exception.getId(),
                "Marked exception as in progress");

        return exception;
    }

    /**
     * Close an exception without resolution
     */
    public TransactionException closeException(UUID exceptionId, String reason, User closedBy) {
        TransactionException exception = exceptionRepository.findById(exceptionId)
                .orElseThrow(() -> new IllegalArgumentException("Exception not found: " + exceptionId));

        exception.setStatus(ExceptionStatus.CLOSED);
        exception.setResolutionNotes(reason);
        exception.setResolvedBy(closedBy);
        exception.setResolvedAt(LocalDateTime.now());

        exception = exceptionRepository.save(exception);

        auditService.logUserAction(closedBy, "EXCEPTION_CLOSED", "TransactionException", exception.getId(),
                "Closed exception: " + reason);

        return exception;
    }

    /**
     * Auto-detect and create exceptions for unpaid transactions
     */
    @Transactional
    public void detectUnpaidTransactions() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(24); // 24 hours old
        
        List<Transaction> unpaidTransactions = transactionRepository.findUnpaidTransactions()
                .stream()
                .filter(t -> t.getTransactionDate().isBefore(cutoffTime))
                .filter(t -> t.getStatus() == TransactionStatus.PENDING)
                .toList();

        for (Transaction transaction : unpaidTransactions) {
            // Check if exception already exists
            boolean exceptionExists = exceptionRepository.findByType(ExceptionType.UNPAID_TRANSACTION)
                    .stream()
                    .anyMatch(e -> e.getTransaction() != null && 
                             e.getTransaction().getId().equals(transaction.getId()));

            if (!exceptionExists) {
                createUnpaidTransactionException(transaction);
            }
        }

        if (!unpaidTransactions.isEmpty()) {
            auditService.logSystemAction("AUTO_DETECTION", "TransactionException", null,
                    "Auto-detected " + unpaidTransactions.size() + " unpaid transactions");
        }
    }

    // Query methods
    public Optional<TransactionException> findById(UUID id) {
        return exceptionRepository.findById(id);
    }

    public List<TransactionException> findOpenExceptions() {
        return exceptionRepository.findOpenExceptions();
    }

    public Page<TransactionException> findExceptionsWithFilters(ExceptionType type, ExceptionStatus status,
                                                               UUID merchantId, LocalDateTime startDate,
                                                               LocalDateTime endDate, Pageable pageable) {
        return exceptionRepository.findWithFilters(type, status, merchantId, startDate, endDate, pageable);
    }

    public Long countOpenExceptions() {
        return exceptionRepository.countOpenExceptions();
    }

    public Long countOpenExceptionsByType(ExceptionType type) {
        return exceptionRepository.countOpenExceptionsByType(type);
    }

    public Long countOpenExceptionsByMerchant(Merchant merchant) {
        return exceptionRepository.countOpenExceptionsByMerchant(merchant);
    }
}

package com.paymentgateway.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paymentgateway.entity.AuditLog;
import com.paymentgateway.entity.User;
import com.paymentgateway.repository.AuditLogRepository;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@Transactional
public class AuditService {

    @Autowired
    private AuditLogRepository auditLogRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public void logUserAction(User user, String action, String entityType, UUID entityId, String description) {
        logUserAction(user, action, entityType, entityId, description, null, null);
    }

    public void logUserAction(User user, String action, String entityType, UUID entityId, 
                             String description, Object oldValues, Object newValues) {
        try {
            AuditLog auditLog = new AuditLog(action, entityType, entityId, user);
            auditLog.setDescription(description);

            // Get request details if available
            ServletRequestAttributes attributes = 
                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                auditLog.setIpAddress(getClientIpAddress(request));
                auditLog.setUserAgent(request.getHeader("User-Agent"));
            }

            // Serialize old and new values
            if (oldValues != null) {
                auditLog.setOldValues(objectMapper.writeValueAsString(oldValues));
            }
            if (newValues != null) {
                auditLog.setNewValues(objectMapper.writeValueAsString(newValues));
            }

            auditLogRepository.save(auditLog);
        } catch (JsonProcessingException e) {
            // Log error but don't fail the main operation
            System.err.println("Failed to serialize audit log values: " + e.getMessage());
        } catch (Exception e) {
            // Log error but don't fail the main operation
            System.err.println("Failed to create audit log: " + e.getMessage());
        }
    }

    public void logSystemAction(String action, String entityType, UUID entityId, String description) {
        try {
            AuditLog auditLog = new AuditLog(action, entityType, entityId, null);
            auditLog.setDescription(description);
            auditLogRepository.save(auditLog);
        } catch (Exception e) {
            System.err.println("Failed to create system audit log: " + e.getMessage());
        }
    }

    public Page<AuditLog> getAuditLogs(Pageable pageable) {
        return auditLogRepository.findAll(pageable);
    }

    public Page<AuditLog> getAuditLogsByUser(User user, Pageable pageable) {
        return auditLogRepository.findByUser(user, pageable);
    }

    public Page<AuditLog> getAuditLogsWithFilters(UUID userId, String action, String entityType,
                                                  LocalDateTime startDate, LocalDateTime endDate,
                                                  Pageable pageable) {
        return auditLogRepository.findWithFilters(userId, action, entityType, startDate, endDate, pageable);
    }

    public List<AuditLog> getAuditLogsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return auditLogRepository.findByTimestampBetween(startDate, endDate);
    }

    public List<String> getAllActions() {
        return auditLogRepository.findAllActions();
    }

    public List<String> getAllEntityTypes() {
        return auditLogRepository.findAllEntityTypes();
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedForHeader = request.getHeader("X-Forwarded-For");
        if (xForwardedForHeader == null) {
            return request.getRemoteAddr();
        } else {
            // X-Forwarded-For can contain multiple IPs, the first one is the original client IP
            return xForwardedForHeader.split(",")[0].trim();
        }
    }
}

package com.paymentgateway.service;

import com.paymentgateway.entity.*;
import com.paymentgateway.enums.CommissionType;
import com.paymentgateway.enums.TransactionType;
import com.paymentgateway.repository.CommissionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@Transactional
public class CommissionService {

    @Autowired
    private CommissionRepository commissionRepository;

    @Autowired
    private AuditService auditService;

    /**
     * Calculate and apply commission for a transaction
     * Commission structure:
     * - Agent gets their commission rate from the transaction amount
     * - Merchant pays their commission rate from the transaction amount  
     * - Platform gets the difference (merchant rate - agent rate)
     * - Net amount to merchant = transaction amount - merchant commission
     */
    public Commission calculateAndApplyCommission(Transaction transaction) {
        if (transaction.getCommission() != null) {
            throw new IllegalStateException("Commission already calculated for transaction: " + 
                    transaction.getTransactionReference());
        }

        Merchant merchant = transaction.getMerchant();
        Agent agent = transaction.getAgent();

        if (merchant == null) {
            throw new IllegalArgumentException("Transaction must have a merchant");
        }

        CommissionType commissionType = getCommissionType(transaction.getType());
        BigDecimal merchantRate = getMerchantCommissionRate(merchant, commissionType);
        BigDecimal agentRate = getAgentCommissionRate(agent, commissionType);

        // Validate commission rates
        if (merchantRate.compareTo(agentRate) < 0) {
            throw new IllegalStateException("Merchant commission rate cannot be less than agent commission rate");
        }

        // Calculate commission amounts
        BigDecimal transactionAmount = transaction.getAmount();
        BigDecimal merchantCommission = calculateCommissionAmount(transactionAmount, merchantRate);
        BigDecimal agentCommission = calculateCommissionAmount(transactionAmount, agentRate);
        BigDecimal platformCommission = merchantCommission.subtract(agentCommission);
        BigDecimal totalCommission = merchantCommission;

        // Calculate net amount for merchant
        BigDecimal netAmount = transactionAmount.subtract(merchantCommission);

        // Create commission record
        Commission commission = new Commission(commissionType, transaction);
        commission.setMerchantRate(merchantRate);
        commission.setAgentRate(agentRate);
        commission.setMerchantCommission(merchantCommission);
        commission.setAgentCommission(agentCommission);
        commission.setPlatformCommission(platformCommission);
        commission.setTotalCommission(totalCommission);
        commission.setMerchant(merchant);
        commission.setAgent(agent);

        commission = commissionRepository.save(commission);

        // Update transaction with commission details
        transaction.setCommission(commission);
        transaction.setCommissionAmount(totalCommission);
        transaction.setNetAmount(netAmount);

        auditService.logSystemAction("COMMISSION_CALCULATED", "Commission", commission.getId(),
                String.format("Commission calculated for transaction %s: Merchant=%.4f%%, Agent=%.4f%%, Platform=%s",
                        transaction.getTransactionReference(), 
                        merchantRate.multiply(BigDecimal.valueOf(100)),
                        agentRate.multiply(BigDecimal.valueOf(100)),
                        platformCommission));

        return commission;
    }

    /**
     * Calculate commission for outgoing transactions (payouts)
     */
    public Commission calculatePayoutCommission(Transaction transaction) {
        if (transaction.getType() != TransactionType.OUTGOING && transaction.getType() != TransactionType.PAYOUT) {
            throw new IllegalArgumentException("Invalid transaction type for payout commission");
        }

        return calculateAndApplyCommission(transaction);
    }

    /**
     * Get commission type based on transaction type
     */
    private CommissionType getCommissionType(TransactionType transactionType) {
        return switch (transactionType) {
            case INCOMING -> CommissionType.INCOMING;
            case OUTGOING, PAYOUT -> CommissionType.OUTGOING;
            default -> throw new IllegalArgumentException("Invalid transaction type for commission: " + transactionType);
        };
    }

    /**
     * Get merchant commission rate based on commission type
     */
    private BigDecimal getMerchantCommissionRate(Merchant merchant, CommissionType commissionType) {
        return switch (commissionType) {
            case INCOMING -> merchant.getIncomingCommissionRate();
            case OUTGOING -> merchant.getOutgoingCommissionRate();
        };
    }

    /**
     * Get agent commission rate based on commission type
     */
    private BigDecimal getAgentCommissionRate(Agent agent, CommissionType commissionType) {
        if (agent == null) {
            return BigDecimal.ZERO;
        }

        return switch (commissionType) {
            case INCOMING -> agent.getIncomingCommissionRate();
            case OUTGOING -> agent.getOutgoingCommissionRate();
        };
    }

    /**
     * Calculate commission amount from rate and transaction amount
     */
    private BigDecimal calculateCommissionAmount(BigDecimal amount, BigDecimal rate) {
        return amount.multiply(rate.divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP))
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * Recalculate commission with new rates (for adjustments)
     */
    public Commission recalculateCommission(Transaction transaction, BigDecimal newMerchantRate, 
                                          BigDecimal newAgentRate, User adjustedBy) {
        Commission existingCommission = transaction.getCommission();
        if (existingCommission == null) {
            throw new IllegalStateException("No existing commission to recalculate");
        }

        // Store old values for audit
        BigDecimal oldMerchantCommission = existingCommission.getMerchantCommission();
        BigDecimal oldAgentCommission = existingCommission.getAgentCommission();
        BigDecimal oldPlatformCommission = existingCommission.getPlatformCommission();

        // Calculate new commission amounts
        BigDecimal transactionAmount = transaction.getAmount();
        BigDecimal newMerchantCommission = calculateCommissionAmount(transactionAmount, newMerchantRate);
        BigDecimal newAgentCommission = calculateCommissionAmount(transactionAmount, newAgentRate);
        BigDecimal newPlatformCommission = newMerchantCommission.subtract(newAgentCommission);
        BigDecimal newNetAmount = transactionAmount.subtract(newMerchantCommission);

        // Update commission
        existingCommission.setMerchantRate(newMerchantRate);
        existingCommission.setAgentRate(newAgentRate);
        existingCommission.setMerchantCommission(newMerchantCommission);
        existingCommission.setAgentCommission(newAgentCommission);
        existingCommission.setPlatformCommission(newPlatformCommission);
        existingCommission.setTotalCommission(newMerchantCommission);

        // Update transaction
        transaction.setCommissionAmount(newMerchantCommission);
        transaction.setNetAmount(newNetAmount);

        existingCommission = commissionRepository.save(existingCommission);

        auditService.logUserAction(adjustedBy, "COMMISSION_ADJUSTED", "Commission", existingCommission.getId(),
                String.format("Commission adjusted for transaction %s: Old rates M=%.4f%%, A=%.4f%% -> New rates M=%.4f%%, A=%.4f%%",
                        transaction.getTransactionReference(),
                        existingCommission.getMerchantRate().multiply(BigDecimal.valueOf(100)),
                        existingCommission.getAgentRate().multiply(BigDecimal.valueOf(100)),
                        newMerchantRate.multiply(BigDecimal.valueOf(100)),
                        newAgentRate.multiply(BigDecimal.valueOf(100))));

        return existingCommission;
    }

    // Query methods
    public Page<Commission> findCommissionsWithFilters(UUID merchantId, UUID agentId, CommissionType type,
                                                      LocalDateTime startDate, LocalDateTime endDate,
                                                      Pageable pageable) {
        return commissionRepository.findWithFilters(merchantId, agentId, type, startDate, endDate, pageable);
    }

    public BigDecimal getTotalMerchantCommission(Merchant merchant) {
        BigDecimal total = commissionRepository.getTotalMerchantCommission(merchant);
        return total != null ? total : BigDecimal.ZERO;
    }

    public BigDecimal getTotalAgentCommission(Agent agent) {
        BigDecimal total = commissionRepository.getTotalAgentCommission(agent);
        return total != null ? total : BigDecimal.ZERO;
    }

    public BigDecimal getTotalPlatformCommission() {
        BigDecimal total = commissionRepository.getTotalPlatformCommission();
        return total != null ? total : BigDecimal.ZERO;
    }

    public BigDecimal getMerchantCommissionByDateRange(Merchant merchant, LocalDateTime startDate, LocalDateTime endDate) {
        BigDecimal total = commissionRepository.getMerchantCommissionByDateRange(merchant, startDate, endDate);
        return total != null ? total : BigDecimal.ZERO;
    }

    public BigDecimal getAgentCommissionByDateRange(Agent agent, LocalDateTime startDate, LocalDateTime endDate) {
        BigDecimal total = commissionRepository.getAgentCommissionByDateRange(agent, startDate, endDate);
        return total != null ? total : BigDecimal.ZERO;
    }

    /**
     * Get commission summary for a merchant
     */
    public CommissionSummary getMerchantCommissionSummary(Merchant merchant, LocalDateTime startDate, LocalDateTime endDate) {
        List<Commission> commissions = commissionRepository.findByMerchantAndCreatedAtBetween(merchant, startDate, endDate);
        
        BigDecimal totalPaid = commissions.stream()
                .map(Commission::getMerchantCommission)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
                
        BigDecimal totalEarned = commissions.stream()
                .map(Commission::getAgentCommission)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return new CommissionSummary(totalPaid, totalEarned, commissions.size());
    }

    /**
     * Commission summary DTO
     */
    public static class CommissionSummary {
        private final BigDecimal totalPaid;
        private final BigDecimal totalEarned;
        private final int transactionCount;

        public CommissionSummary(BigDecimal totalPaid, BigDecimal totalEarned, int transactionCount) {
            this.totalPaid = totalPaid;
            this.totalEarned = totalEarned;
            this.transactionCount = transactionCount;
        }

        public BigDecimal getTotalPaid() { return totalPaid; }
        public BigDecimal getTotalEarned() { return totalEarned; }
        public int getTransactionCount() { return transactionCount; }
    }
}

package com.paymentgateway.service;

import com.paymentgateway.dto.auth.LoginRequest;
import com.paymentgateway.dto.auth.LoginResponse;
import com.paymentgateway.entity.User;
import com.paymentgateway.repository.UserRepository;
import com.paymentgateway.security.CustomUserDetailsService;
import com.paymentgateway.security.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional
public class AuthService {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private CustomUserDetailsService userDetailsService;

    @Autowired
    private AuditService auditService;

    public LoginResponse login(LoginRequest loginRequest) {
        try {
            // Authenticate user
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getUsername(),
                            loginRequest.getPassword()
                    )
            );

            // Get user details
            CustomUserDetailsService.CustomUserPrincipal userPrincipal = 
                    (CustomUserDetailsService.CustomUserPrincipal) authentication.getPrincipal();
            User user = userPrincipal.getUser();

            // Update last login time and reset failed attempts
            user.setLastLoginAt(LocalDateTime.now());
            user.setFailedLoginAttempts(0);
            userRepository.save(user);

            // Generate JWT token
            Map<String, Object> extraClaims = new HashMap<>();
            extraClaims.put("userId", user.getId().toString());
            extraClaims.put("email", user.getEmail());
            extraClaims.put("roles", user.getRoles().stream()
                    .map(role -> role.getName())
                    .collect(Collectors.toSet()));

            String token = jwtUtil.generateToken(userPrincipal, extraClaims);

            // Prepare response
            Set<String> roles = user.getRoles().stream()
                    .map(role -> role.getName())
                    .collect(Collectors.toSet());

            Set<String> permissions = user.getRoles().stream()
                    .flatMap(role -> role.getPermissions().stream())
                    .map(permission -> permission.getName())
                    .collect(Collectors.toSet());

            LocalDateTime expiresAt = LocalDateTime.now()
                    .plusSeconds(jwtUtil.getExpirationTime() / 1000);

            // Log successful login
            auditService.logUserAction(user, "LOGIN", "User", user.getId(), 
                    "User logged in successfully");

            return new LoginResponse(token, user.getUsername(), user.getEmail(), 
                    user.getFullName(), roles, permissions, expiresAt);

        } catch (AuthenticationException e) {
            // Handle failed login attempt
            handleFailedLogin(loginRequest.getUsername());
            throw new BadCredentialsException("Invalid username or password");
        }
    }

    private void handleFailedLogin(String username) {
        userRepository.findByUsernameOrEmail(username, username)
                .ifPresent(user -> {
                    user.setFailedLoginAttempts(user.getFailedLoginAttempts() + 1);
                    
                    // Lock user after 5 failed attempts
                    if (user.getFailedLoginAttempts() >= 5) {
                        user.setIsLocked(true);
                        auditService.logUserAction(user, "ACCOUNT_LOCKED", "User", user.getId(), 
                                "Account locked due to multiple failed login attempts");
                    }
                    
                    userRepository.save(user);
                    auditService.logUserAction(user, "LOGIN_FAILED", "User", user.getId(), 
                            "Failed login attempt #" + user.getFailedLoginAttempts());
                });
    }

    public void logout(String username) {
        userRepository.findByUsernameOrEmail(username, username)
                .ifPresent(user -> {
                    auditService.logUserAction(user, "LOGOUT", "User", user.getId(), 
                            "User logged out");
                });
    }

    public LoginResponse refreshToken(String token) {
        if (jwtUtil.validateToken(token)) {
            String username = jwtUtil.extractUsername(token);
            UserDetails userDetails = userDetailsService.loadUserByUsername(username);
            
            if (userDetails instanceof CustomUserDetailsService.CustomUserPrincipal) {
                CustomUserDetailsService.CustomUserPrincipal userPrincipal = 
                        (CustomUserDetailsService.CustomUserPrincipal) userDetails;
                User user = userPrincipal.getUser();

                String newToken = jwtUtil.refreshToken(token);
                
                Set<String> roles = user.getRoles().stream()
                        .map(role -> role.getName())
                        .collect(Collectors.toSet());

                Set<String> permissions = user.getRoles().stream()
                        .flatMap(role -> role.getPermissions().stream())
                        .map(permission -> permission.getName())
                        .collect(Collectors.toSet());

                LocalDateTime expiresAt = LocalDateTime.now()
                        .plusSeconds(jwtUtil.getExpirationTime() / 1000);

                return new LoginResponse(newToken, user.getUsername(), user.getEmail(), 
                        user.getFullName(), roles, permissions, expiresAt);
            }
        }
        throw new BadCredentialsException("Invalid token");
    }
}

package com.paymentgateway.service;

import com.paymentgateway.entity.*;
import com.paymentgateway.enums.*;
import com.paymentgateway.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class TransactionService {

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private MerchantRepository merchantRepository;

    @Autowired
    private AgentRepository agentRepository;

    @Autowired
    private BankAccountRepository bankAccountRepository;

    @Autowired
    private CommissionService commissionService;

    @Autowired
    private ExceptionService exceptionService;

    @Autowired
    private AuditService auditService;

    /**
     * Create an expected transaction from merchant
     */
    public Transaction createExpectedTransaction(String merchantCode, String externalReference, 
                                               BigDecimal amount, String description, User createdBy) {
        
        Merchant merchant = merchantRepository.findByMerchantCode(merchantCode)
                .orElseThrow(() -> new IllegalArgumentException("Merchant not found: " + merchantCode));

        if (!merchant.getIsActive()) {
            throw new IllegalArgumentException("Merchant is not active: " + merchantCode);
        }

        // Check for duplicate external reference
        if (externalReference != null && transactionRepository.existsByExternalReference(externalReference)) {
            throw new IllegalArgumentException("Duplicate external reference: " + externalReference);
        }

        Transaction transaction = new Transaction();
        transaction.setTransactionReference(generateTransactionReference());
        transaction.setExternalReference(externalReference);
        transaction.setType(TransactionType.INCOMING);
        transaction.setStatus(TransactionStatus.PENDING);
        transaction.setAmount(amount);
        transaction.setNetAmount(amount); // Will be updated after commission calculation
        transaction.setTransactionDate(LocalDateTime.now());
        transaction.setDescription(description);
        transaction.setMerchant(merchant);
        transaction.setAgent(merchant.getAgent());
        transaction.setIsExpected(true);
        transaction.setIsBankConfirmed(false);

        transaction = transactionRepository.save(transaction);

        auditService.logUserAction(createdBy, "CREATE", "Transaction", transaction.getId(),
                "Created expected transaction: " + transaction.getTransactionReference());

        return transaction;
    }

    /**
     * Process incoming bank transaction
     */
    public Transaction processBankTransaction(String bankAccountNumber, BigDecimal amount, 
                                            String bankReference, LocalDateTime transactionDate, 
                                            String description, User processedBy) {
        
        BankAccount bankAccount = bankAccountRepository.findByAccountNumber(bankAccountNumber)
                .orElseThrow(() -> new IllegalArgumentException("Bank account not found: " + bankAccountNumber));

        if (!bankAccount.getIsActive()) {
            throw new IllegalArgumentException("Bank account is not active: " + bankAccountNumber);
        }

        Transaction transaction = new Transaction();
        transaction.setTransactionReference(generateTransactionReference());
        transaction.setExternalReference(bankReference);
        transaction.setType(TransactionType.INCOMING);
        transaction.setStatus(TransactionStatus.PROCESSING);
        transaction.setAmount(amount);
        transaction.setNetAmount(amount);
        transaction.setTransactionDate(transactionDate);
        transaction.setDescription(description);
        transaction.setBankAccount(bankAccount);
        transaction.setIsExpected(false);
        transaction.setIsBankConfirmed(true);

        transaction = transactionRepository.save(transaction);

        // Update bank account balance
        bankAccount.setCurrentBalance(bankAccount.getCurrentBalance().add(amount));
        bankAccountRepository.save(bankAccount);

        // Try to match with expected transactions
        matchTransaction(transaction);

        auditService.logUserAction(processedBy, "CREATE", "Transaction", transaction.getId(),
                "Processed bank transaction: " + transaction.getTransactionReference());

        return transaction;
    }

    /**
     * Match bank transaction with expected merchant transaction
     */
    public void matchTransaction(Transaction bankTransaction) {
        if (!bankTransaction.getIsBankConfirmed()) {
            throw new IllegalArgumentException("Transaction is not bank confirmed");
        }

        // Find matching expected transactions
        List<Transaction> expectedTransactions = findMatchingExpectedTransactions(bankTransaction);

        if (expectedTransactions.isEmpty()) {
            // Create unrecognized transaction exception
            exceptionService.createUnrecognizedTransactionException(bankTransaction);
            return;
        }

        if (expectedTransactions.size() > 1) {
            // Multiple matches - create exception for manual resolution
            exceptionService.createMultipleMatchException(bankTransaction, expectedTransactions);
            return;
        }

        Transaction expectedTransaction = expectedTransactions.get(0);
        
        // Validate amount match
        if (!bankTransaction.getAmount().equals(expectedTransaction.getAmount())) {
            exceptionService.createAmountMismatchException(bankTransaction, expectedTransaction);
            return;
        }

        // Match the transactions
        performTransactionMatch(bankTransaction, expectedTransaction);
    }

    /**
     * Perform the actual transaction matching
     */
    private void performTransactionMatch(Transaction bankTransaction, Transaction expectedTransaction) {
        // Update expected transaction
        expectedTransaction.setIsBankConfirmed(true);
        expectedTransaction.setMatchedAt(LocalDateTime.now());
        expectedTransaction.setStatus(TransactionStatus.MATCHED);
        expectedTransaction.setBankAccount(bankTransaction.getBankAccount());

        // Calculate and apply commissions
        commissionService.calculateAndApplyCommission(expectedTransaction);

        // Update merchant balance
        updateMerchantBalance(expectedTransaction);

        // Update agent balance if applicable
        if (expectedTransaction.getAgent() != null) {
            updateAgentBalance(expectedTransaction);
        }

        // Mark bank transaction as matched
        bankTransaction.setStatus(TransactionStatus.MATCHED);
        bankTransaction.setMerchant(expectedTransaction.getMerchant());
        bankTransaction.setAgent(expectedTransaction.getAgent());

        transactionRepository.save(expectedTransaction);
        transactionRepository.save(bankTransaction);

        auditService.logSystemAction("MATCH", "Transaction", expectedTransaction.getId(),
                "Matched transactions: " + bankTransaction.getTransactionReference() + 
                " with " + expectedTransaction.getTransactionReference());
    }

    /**
     * Find matching expected transactions based on amount and time window
     */
    private List<Transaction> findMatchingExpectedTransactions(Transaction bankTransaction) {
        LocalDateTime startTime = bankTransaction.getTransactionDate().minusHours(24);
        LocalDateTime endTime = bankTransaction.getTransactionDate().plusHours(24);

        return transactionRepository.findByTransactionDateBetween(startTime, endTime)
                .stream()
                .filter(t -> t.getIsExpected() && !t.getIsBankConfirmed())
                .filter(t -> t.getAmount().equals(bankTransaction.getAmount()))
                .filter(t -> t.getStatus() == TransactionStatus.PENDING)
                .toList();
    }

    /**
     * Update merchant balance after successful transaction
     */
    private void updateMerchantBalance(Transaction transaction) {
        Merchant merchant = transaction.getMerchant();
        BigDecimal netAmount = transaction.getNetAmount();
        
        merchant.setCurrentBalance(merchant.getCurrentBalance().add(netAmount));
        merchantRepository.save(merchant);

        auditService.logSystemAction("BALANCE_UPDATE", "Merchant", merchant.getId(),
                "Updated balance by " + netAmount + " for transaction " + transaction.getTransactionReference());
    }

    /**
     * Update agent balance after commission
     */
    private void updateAgentBalance(Transaction transaction) {
        Agent agent = transaction.getAgent();
        if (agent != null && transaction.getCommission() != null) {
            BigDecimal agentCommission = transaction.getCommission().getAgentCommission();
            
            agent.setCurrentBalance(agent.getCurrentBalance().add(agentCommission));
            agentRepository.save(agent);

            auditService.logSystemAction("BALANCE_UPDATE", "Agent", agent.getId(),
                    "Updated balance by " + agentCommission + " for transaction " + transaction.getTransactionReference());
        }
    }

    /**
     * Generate unique transaction reference
     */
    private String generateTransactionReference() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String random = UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        return "TXN" + timestamp + random;
    }

    // Query methods
    public Optional<Transaction> findByTransactionReference(String transactionReference) {
        return transactionRepository.findByTransactionReference(transactionReference);
    }

    public Page<Transaction> findTransactionsWithFilters(UUID merchantId, UUID agentId, UUID bankAccountId,
                                                        TransactionStatus status, TransactionType type,
                                                        LocalDateTime startDate, LocalDateTime endDate,
                                                        Pageable pageable) {
        return transactionRepository.findWithFilters(merchantId, agentId, bankAccountId, status, type,
                startDate, endDate, pageable);
    }

    public List<Transaction> findUnpaidTransactions() {
        return transactionRepository.findUnpaidTransactions();
    }

    public List<Transaction> findUnrecognizedTransactions() {
        return transactionRepository.findUnrecognizedTransactions();
    }

    public BigDecimal getTotalAmountByMerchant(Merchant merchant) {
        return transactionRepository.getTotalAmountByMerchant(merchant);
    }
}

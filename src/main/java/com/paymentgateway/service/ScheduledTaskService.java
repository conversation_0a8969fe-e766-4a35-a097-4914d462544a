package com.paymentgateway.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
public class ScheduledTaskService {

    private static final Logger logger = LoggerFactory.getLogger(ScheduledTaskService.class);

    @Autowired
    private ExceptionService exceptionService;

    @Autowired
    private AuditService auditService;

    /**
     * Auto-detect unpaid transactions every hour
     */
    @Scheduled(fixedRate = 3600000) // 1 hour = 3,600,000 milliseconds
    public void detectUnpaidTransactions() {
        try {
            logger.info("Starting scheduled unpaid transaction detection");
            exceptionService.detectUnpaidTransactions();
            logger.info("Completed scheduled unpaid transaction detection");
        } catch (Exception e) {
            logger.error("Error during scheduled unpaid transaction detection", e);
            auditService.logSystemAction("SCHEDULED_TASK_ERROR", "ScheduledTask", null,
                    "Error during unpaid transaction detection: " + e.getMessage());
        }
    }

    /**
     * Clean up old audit logs (keep last 90 days)
     */
    @Scheduled(cron = "0 0 2 * * ?") // Daily at 2 AM
    public void cleanupOldAuditLogs() {
        try {
            logger.info("Starting scheduled audit log cleanup");
            // Implementation would go here to delete old audit logs
            // For now, just log the action
            auditService.logSystemAction("SCHEDULED_CLEANUP", "AuditLog", null,
                    "Scheduled audit log cleanup executed");
            logger.info("Completed scheduled audit log cleanup");
        } catch (Exception e) {
            logger.error("Error during scheduled audit log cleanup", e);
            auditService.logSystemAction("SCHEDULED_TASK_ERROR", "ScheduledTask", null,
                    "Error during audit log cleanup: " + e.getMessage());
        }
    }

    /**
     * Generate daily summary reports
     */
    @Scheduled(cron = "0 30 1 * * ?") // Daily at 1:30 AM
    public void generateDailySummaryReports() {
        try {
            logger.info("Starting scheduled daily summary report generation");
            // Implementation would go here to generate and email daily reports
            auditService.logSystemAction("SCHEDULED_REPORT", "Report", null,
                    "Daily summary reports generated");
            logger.info("Completed scheduled daily summary report generation");
        } catch (Exception e) {
            logger.error("Error during scheduled daily summary report generation", e);
            auditService.logSystemAction("SCHEDULED_TASK_ERROR", "ScheduledTask", null,
                    "Error during daily summary report generation: " + e.getMessage());
        }
    }
}

package com.paymentgateway.service;

import com.paymentgateway.entity.*;
import com.paymentgateway.enums.TransactionStatus;
import com.paymentgateway.enums.TransactionType;
import com.paymentgateway.repository.*;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

@Service
@Transactional(readOnly = true)
public class ReportService {

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private CommissionRepository commissionRepository;

    @Autowired
    private MerchantRepository merchantRepository;

    @Autowired
    private AgentRepository agentRepository;

    @Autowired
    private TransactionExceptionRepository exceptionRepository;

    @Autowired
    private AuditService auditService;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Generate transaction report in CSV format
     */
    public byte[] generateTransactionReport(UUID merchantId, UUID agentId, UUID bankAccountId,
                                          TransactionStatus status, TransactionType type,
                                          LocalDateTime startDate, LocalDateTime endDate,
                                          User requestedBy) throws IOException {

        List<Transaction> transactions = transactionRepository.findWithFilters(
                merchantId, agentId, bankAccountId, status, type, startDate, endDate, null)
                .getContent();

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream);
        CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(
                "Transaction Reference", "External Reference", "Type", "Status", "Amount",
                "Commission Amount", "Net Amount", "Transaction Date", "Description",
                "Merchant Code", "Merchant Name", "Agent Code", "Agent Name",
                "Bank Account", "Is Expected", "Is Bank Confirmed", "Matched At",
                "Created At", "Updated At"
        ));

        for (Transaction transaction : transactions) {
            csvPrinter.printRecord(
                    transaction.getTransactionReference(),
                    transaction.getExternalReference(),
                    transaction.getType().name(),
                    transaction.getStatus().name(),
                    transaction.getAmount(),
                    transaction.getCommissionAmount(),
                    transaction.getNetAmount(),
                    formatDateTime(transaction.getTransactionDate()),
                    transaction.getDescription(),
                    transaction.getMerchant() != null ? transaction.getMerchant().getMerchantCode() : "",
                    transaction.getMerchant() != null ? transaction.getMerchant().getName() : "",
                    transaction.getAgent() != null ? transaction.getAgent().getAgentCode() : "",
                    transaction.getAgent() != null ? transaction.getAgent().getName() : "",
                    transaction.getBankAccount() != null ? transaction.getBankAccount().getAccountNumber() : "",
                    transaction.getIsExpected(),
                    transaction.getIsBankConfirmed(),
                    formatDateTime(transaction.getMatchedAt()),
                    formatDateTime(transaction.getCreatedAt()),
                    formatDateTime(transaction.getUpdatedAt())
            );
        }

        csvPrinter.flush();
        csvPrinter.close();

        auditService.logUserAction(requestedBy, "REPORT_EXPORT", "Transaction", null,
                "Exported transaction report with " + transactions.size() + " records");

        return outputStream.toByteArray();
    }

    /**
     * Generate commission report in CSV format
     */
    public byte[] generateCommissionReport(UUID merchantId, UUID agentId,
                                         LocalDateTime startDate, LocalDateTime endDate,
                                         User requestedBy) throws IOException {

        List<Commission> commissions = commissionRepository.findWithFilters(
                merchantId, agentId, null, startDate, endDate, null).getContent();

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream);
        CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(
                "Transaction Reference", "Commission Type", "Merchant Code", "Merchant Name",
                "Agent Code", "Agent Name", "Merchant Rate", "Agent Rate",
                "Merchant Commission", "Agent Commission", "Platform Commission",
                "Total Commission", "Transaction Amount", "Created At"
        ));

        for (Commission commission : commissions) {
            Transaction transaction = commission.getTransaction();
            csvPrinter.printRecord(
                    transaction.getTransactionReference(),
                    commission.getType().name(),
                    commission.getMerchant() != null ? commission.getMerchant().getMerchantCode() : "",
                    commission.getMerchant() != null ? commission.getMerchant().getName() : "",
                    commission.getAgent() != null ? commission.getAgent().getAgentCode() : "",
                    commission.getAgent() != null ? commission.getAgent().getName() : "",
                    commission.getMerchantRate(),
                    commission.getAgentRate(),
                    commission.getMerchantCommission(),
                    commission.getAgentCommission(),
                    commission.getPlatformCommission(),
                    commission.getTotalCommission(),
                    transaction.getAmount(),
                    formatDateTime(commission.getCreatedAt())
            );
        }

        csvPrinter.flush();
        csvPrinter.close();

        auditService.logUserAction(requestedBy, "REPORT_EXPORT", "Commission", null,
                "Exported commission report with " + commissions.size() + " records");

        return outputStream.toByteArray();
    }

    /**
     * Generate merchant summary report
     */
    public byte[] generateMerchantSummaryReport(LocalDateTime startDate, LocalDateTime endDate,
                                              User requestedBy) throws IOException {

        List<Merchant> merchants = merchantRepository.findByIsActiveTrue();

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream);
        CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(
                "Merchant Code", "Merchant Name", "Agent Code", "Agent Name",
                "Current Balance", "Total Transactions", "Total Amount", "Total Commission Paid",
                "Incoming Commission Rate", "Outgoing Commission Rate", "Is Active"
        ));

        for (Merchant merchant : merchants) {
            List<Transaction> transactions = transactionRepository.findByMerchantAndTransactionDateBetween(
                    merchant, startDate, endDate);

            BigDecimal totalAmount = transactions.stream()
                    .map(Transaction::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalCommission = commissionRepository.getMerchantCommissionByDateRange(
                    merchant, startDate, endDate);

            csvPrinter.printRecord(
                    merchant.getMerchantCode(),
                    merchant.getName(),
                    merchant.getAgent() != null ? merchant.getAgent().getAgentCode() : "",
                    merchant.getAgent() != null ? merchant.getAgent().getName() : "",
                    merchant.getCurrentBalance(),
                    transactions.size(),
                    totalAmount,
                    totalCommission != null ? totalCommission : BigDecimal.ZERO,
                    merchant.getIncomingCommissionRate(),
                    merchant.getOutgoingCommissionRate(),
                    merchant.getIsActive()
            );
        }

        csvPrinter.flush();
        csvPrinter.close();

        auditService.logUserAction(requestedBy, "REPORT_EXPORT", "Merchant", null,
                "Exported merchant summary report with " + merchants.size() + " records");

        return outputStream.toByteArray();
    }

    /**
     * Generate agent summary report
     */
    public byte[] generateAgentSummaryReport(LocalDateTime startDate, LocalDateTime endDate,
                                           User requestedBy) throws IOException {

        List<Agent> agents = agentRepository.findByIsActiveTrue();

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream);
        CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(
                "Agent Code", "Agent Name", "Current Balance", "Total Merchants",
                "Total Transactions", "Total Commission Earned", "Incoming Commission Rate",
                "Outgoing Commission Rate", "Is Active"
        ));

        for (Agent agent : agents) {
            List<Merchant> agentMerchants = merchantRepository.findByAgentAndIsActiveTrue(agent);
            
            List<Transaction> transactions = transactionRepository.findByAgentAndTransactionDateBetween(
                    agent, startDate, endDate);

            BigDecimal totalCommission = commissionRepository.getAgentCommissionByDateRange(
                    agent, startDate, endDate);

            csvPrinter.printRecord(
                    agent.getAgentCode(),
                    agent.getName(),
                    agent.getCurrentBalance(),
                    agentMerchants.size(),
                    transactions.size(),
                    totalCommission != null ? totalCommission : BigDecimal.ZERO,
                    agent.getIncomingCommissionRate(),
                    agent.getOutgoingCommissionRate(),
                    agent.getIsActive()
            );
        }

        csvPrinter.flush();
        csvPrinter.close();

        auditService.logUserAction(requestedBy, "REPORT_EXPORT", "Agent", null,
                "Exported agent summary report with " + agents.size() + " records");

        return outputStream.toByteArray();
    }

    /**
     * Generate exception report
     */
    public byte[] generateExceptionReport(LocalDateTime startDate, LocalDateTime endDate,
                                        User requestedBy) throws IOException {

        List<TransactionException> exceptions = exceptionRepository.findByCreatedAtBetween(startDate, endDate);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream);
        CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(
                "Exception Type", "Status", "Description", "Transaction Reference",
                "Merchant Code", "Bank Account", "Resolution Notes", "Created At",
                "Resolved At", "Resolved By"
        ));

        for (TransactionException exception : exceptions) {
            csvPrinter.printRecord(
                    exception.getType().name(),
                    exception.getStatus().name(),
                    exception.getDescription(),
                    exception.getTransaction() != null ? exception.getTransaction().getTransactionReference() : "",
                    exception.getMerchant() != null ? exception.getMerchant().getMerchantCode() : "",
                    exception.getBankAccount() != null ? exception.getBankAccount().getAccountNumber() : "",
                    exception.getResolutionNotes(),
                    formatDateTime(exception.getCreatedAt()),
                    formatDateTime(exception.getResolvedAt()),
                    exception.getResolvedBy() != null ? exception.getResolvedBy().getUsername() : ""
            );
        }

        csvPrinter.flush();
        csvPrinter.close();

        auditService.logUserAction(requestedBy, "REPORT_EXPORT", "Exception", null,
                "Exported exception report with " + exceptions.size() + " records");

        return outputStream.toByteArray();
    }

    /**
     * Get dashboard statistics
     */
    public DashboardStats getDashboardStats() {
        DashboardStats stats = new DashboardStats();
        
        // Transaction counts
        stats.setTotalTransactions(transactionRepository.count());
        stats.setPendingTransactions(transactionRepository.findByStatus(TransactionStatus.PENDING).size());
        stats.setMatchedTransactions(transactionRepository.findByStatus(TransactionStatus.MATCHED).size());
        stats.setExceptionTransactions(transactionRepository.findByStatus(TransactionStatus.EXCEPTION).size());
        
        // Exception counts
        stats.setOpenExceptions(exceptionRepository.countOpenExceptions());
        
        // Entity counts
        stats.setTotalMerchants(merchantRepository.findByIsActiveTrue().size());
        stats.setTotalAgents(agentRepository.findByIsActiveTrue().size());
        
        // Commission totals
        stats.setTotalPlatformCommission(commissionRepository.getTotalPlatformCommission());
        
        return stats;
    }

    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_FORMATTER) : "";
    }

    /**
     * Dashboard statistics DTO
     */
    public static class DashboardStats {
        private long totalTransactions;
        private long pendingTransactions;
        private long matchedTransactions;
        private long exceptionTransactions;
        private long openExceptions;
        private long totalMerchants;
        private long totalAgents;
        private BigDecimal totalPlatformCommission;

        // Getters and setters
        public long getTotalTransactions() { return totalTransactions; }
        public void setTotalTransactions(long totalTransactions) { this.totalTransactions = totalTransactions; }
        public long getPendingTransactions() { return pendingTransactions; }
        public void setPendingTransactions(long pendingTransactions) { this.pendingTransactions = pendingTransactions; }
        public long getMatchedTransactions() { return matchedTransactions; }
        public void setMatchedTransactions(long matchedTransactions) { this.matchedTransactions = matchedTransactions; }
        public long getExceptionTransactions() { return exceptionTransactions; }
        public void setExceptionTransactions(long exceptionTransactions) { this.exceptionTransactions = exceptionTransactions; }
        public long getOpenExceptions() { return openExceptions; }
        public void setOpenExceptions(long openExceptions) { this.openExceptions = openExceptions; }
        public long getTotalMerchants() { return totalMerchants; }
        public void setTotalMerchants(long totalMerchants) { this.totalMerchants = totalMerchants; }
        public long getTotalAgents() { return totalAgents; }
        public void setTotalAgents(long totalAgents) { this.totalAgents = totalAgents; }
        public BigDecimal getTotalPlatformCommission() { return totalPlatformCommission; }
        public void setTotalPlatformCommission(BigDecimal totalPlatformCommission) { this.totalPlatformCommission = totalPlatformCommission; }
    }
}

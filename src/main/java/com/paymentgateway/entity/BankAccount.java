package com.paymentgateway.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;

@Entity
@Table(name = "bank_accounts", indexes = {
    @Index(name = "idx_bank_account_number", columnList = "account_number"),
    @Index(name = "idx_bank_account_nickname", columnList = "nickname")
})
public class BankAccount extends BaseEntity {

    @NotBlank
    @Size(max = 100)
    @Column(name = "account_number", nullable = false, unique = true, length = 100)
    private String accountNumber;

    @NotBlank
    @Size(max = 100)
    @Column(name = "account_name", nullable = false, length = 100)
    private String accountName;

    @NotBlank
    @Size(max = 100)
    @Column(name = "bank_name", nullable = false, length = 100)
    private String bankName;

    @Size(max = 50)
    @Column(name = "nickname", length = 50)
    private String nickname;

    @NotNull
    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "current_balance", nullable = false, precision = 19, scale = 2)
    private BigDecimal currentBalance = BigDecimal.ZERO;

    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "min_balance_limit", precision = 19, scale = 2)
    private BigDecimal minBalanceLimit;

    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "max_balance_limit", precision = 19, scale = 2)
    private BigDecimal maxBalanceLimit;

    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "daily_in_limit", precision = 19, scale = 2)
    private BigDecimal dailyInLimit;

    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "daily_out_limit", precision = 19, scale = 2)
    private BigDecimal dailyOutLimit;

    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "monthly_in_limit", precision = 19, scale = 2)
    private BigDecimal monthlyInLimit;

    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "monthly_out_limit", precision = 19, scale = 2)
    private BigDecimal monthlyOutLimit;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Size(max = 500)
    @Column(name = "notes", length = 500)
    private String notes;

    // Constructors
    public BankAccount() {}

    public BankAccount(String accountNumber, String accountName, String bankName) {
        this.accountNumber = accountNumber;
        this.accountName = accountName;
        this.bankName = bankName;
    }

    // Getters and Setters
    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public BigDecimal getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(BigDecimal currentBalance) {
        this.currentBalance = currentBalance;
    }

    public BigDecimal getMinBalanceLimit() {
        return minBalanceLimit;
    }

    public void setMinBalanceLimit(BigDecimal minBalanceLimit) {
        this.minBalanceLimit = minBalanceLimit;
    }

    public BigDecimal getMaxBalanceLimit() {
        return maxBalanceLimit;
    }

    public void setMaxBalanceLimit(BigDecimal maxBalanceLimit) {
        this.maxBalanceLimit = maxBalanceLimit;
    }

    public BigDecimal getDailyInLimit() {
        return dailyInLimit;
    }

    public void setDailyInLimit(BigDecimal dailyInLimit) {
        this.dailyInLimit = dailyInLimit;
    }

    public BigDecimal getDailyOutLimit() {
        return dailyOutLimit;
    }

    public void setDailyOutLimit(BigDecimal dailyOutLimit) {
        this.dailyOutLimit = dailyOutLimit;
    }

    public BigDecimal getMonthlyInLimit() {
        return monthlyInLimit;
    }

    public void setMonthlyInLimit(BigDecimal monthlyInLimit) {
        this.monthlyInLimit = monthlyInLimit;
    }

    public BigDecimal getMonthlyOutLimit() {
        return monthlyOutLimit;
    }

    public void setMonthlyOutLimit(BigDecimal monthlyOutLimit) {
        this.monthlyOutLimit = monthlyOutLimit;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getDisplayName() {
        return nickname != null ? nickname : (bankName + " - " + accountNumber);
    }
}

package com.paymentgateway.entity;

import com.paymentgateway.enums.CommissionType;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;

@Entity
@Table(name = "commissions", indexes = {
    @Index(name = "idx_commission_transaction", columnList = "transaction_id"),
    @Index(name = "idx_commission_merchant", columnList = "merchant_id"),
    @Index(name = "idx_commission_agent", columnList = "agent_id"),
    @Index(name = "idx_commission_type", columnList = "type")
})
public class Commission extends BaseEntity {

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, length = 20)
    private CommissionType type;

    @NotNull
    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "merchant_rate", nullable = false, precision = 5, scale = 4)
    private BigDecimal merchantRate;

    @NotNull
    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "agent_rate", nullable = false, precision = 5, scale = 4)
    private BigDecimal agentRate;

    // todo: check with is merchant commission
    @NotNull
    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "merchant_commission", nullable = false, precision = 19, scale = 2)
    private BigDecimal merchantCommission;

    @NotNull
    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "agent_commission", nullable = false, precision = 19, scale = 2)
    private BigDecimal agentCommission;

    @NotNull
    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "platform_commission", nullable = false, precision = 19, scale = 2)
    private BigDecimal platformCommission;

    @NotNull
    @DecimalMin(value = "0.0", inclusive = true)
    @Column(name = "total_commission", nullable = false, precision = 19, scale = 2)
    private BigDecimal totalCommission;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "transaction_id", nullable = false)
    private Transaction transaction;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "merchant_id")
    private Merchant merchant;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id")
    private Agent agent;

    // Constructors
    public Commission() {}

    public Commission(CommissionType type, Transaction transaction) {
        this.type = type;
        this.transaction = transaction;
    }

    // Getters and Setters
    public CommissionType getType() {
        return type;
    }

    public void setType(CommissionType type) {
        this.type = type;
    }

    public BigDecimal getMerchantRate() {
        return merchantRate;
    }

    public void setMerchantRate(BigDecimal merchantRate) {
        this.merchantRate = merchantRate;
    }

    public BigDecimal getAgentRate() {
        return agentRate;
    }

    public void setAgentRate(BigDecimal agentRate) {
        this.agentRate = agentRate;
    }

    public BigDecimal getMerchantCommission() {
        return merchantCommission;
    }

    public void setMerchantCommission(BigDecimal merchantCommission) {
        this.merchantCommission = merchantCommission;
    }

    public BigDecimal getAgentCommission() {
        return agentCommission;
    }

    public void setAgentCommission(BigDecimal agentCommission) {
        this.agentCommission = agentCommission;
    }

    public BigDecimal getPlatformCommission() {
        return platformCommission;
    }

    public void setPlatformCommission(BigDecimal platformCommission) {
        this.platformCommission = platformCommission;
    }

    public BigDecimal getTotalCommission() {
        return totalCommission;
    }

    public void setTotalCommission(BigDecimal totalCommission) {
        this.totalCommission = totalCommission;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public Merchant getMerchant() {
        return merchant;
    }

    public void setMerchant(Merchant merchant) {
        this.merchant = merchant;
    }

    public Agent getAgent() {
        return agent;
    }

    public void setAgent(Agent agent) {
        this.agent = agent;
    }
}

package com.paymentgateway.dto.external;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;

public class ExternalTransactionRequest {

    @Size(max = 100, message = "External reference must not exceed 100 characters")
    private String externalReference;

    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    private BigDecimal amount;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    // Optional callback URL for webhook notifications
    @Size(max = 500, message = "Callback URL must not exceed 500 characters")
    private String callbackUrl;

    // Constructors
    public ExternalTransactionRequest() {}

    public ExternalTransactionRequest(String externalReference, BigDecimal amount, String description) {
        this.externalReference = externalReference;
        this.amount = amount;
        this.description = description;
    }

    // Getters and Setters
    public String getExternalReference() {
        return externalReference;
    }

    public void setExternalReference(String externalReference) {
        this.externalReference = externalReference;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }
}

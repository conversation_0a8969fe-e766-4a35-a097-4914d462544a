package com.paymentgateway.dto.exception;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

public class ResolveExceptionRequest {

    @NotBlank(message = "Resolution notes are required")
    @Size(max = 2000, message = "Resolution notes must not exceed 2000 characters")
    private String resolutionNotes;

    // Constructors
    public ResolveExceptionRequest() {}

    public ResolveExceptionRequest(String resolutionNotes) {
        this.resolutionNotes = resolutionNotes;
    }

    // Getters and Setters
    public String getResolutionNotes() {
        return resolutionNotes;
    }

    public void setResolutionNotes(String resolutionNotes) {
        this.resolutionNotes = resolutionNotes;
    }
}

package com.paymentgateway.dto.transaction;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;

public class CreateTransactionRequest {

    @NotBlank(message = "Merchant code is required")
    @Size(max = 50, message = "Merchant code must not exceed 50 characters")
    private String merchantCode;

    @Size(max = 100, message = "External reference must not exceed 100 characters")
    private String externalReference;

    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    private BigDecimal amount;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    // Constructors
    public CreateTransactionRequest() {}

    public CreateTransactionRequest(String merchantCode, String externalReference, BigDecimal amount, String description) {
        this.merchantCode = merchantCode;
        this.externalReference = externalReference;
        this.amount = amount;
        this.description = description;
    }

    // Getters and Setters
    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getExternalReference() {
        return externalReference;
    }

    public void setExternalReference(String externalReference) {
        this.externalReference = externalReference;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}

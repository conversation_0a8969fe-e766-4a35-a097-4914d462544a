package com.paymentgateway.dto.transaction;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class BankTransactionRequest {

    @NotBlank(message = "Bank account number is required")
    @Size(max = 100, message = "Bank account number must not exceed 100 characters")
    private String bankAccountNumber;

    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    private BigDecimal amount;

    @Size(max = 100, message = "Bank reference must not exceed 100 characters")
    private String bankReference;

    @NotNull(message = "Transaction date is required")
    private LocalDateTime transactionDate;

    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;

    // Constructors
    public BankTransactionRequest() {}

    public BankTransactionRequest(String bankAccountNumber, BigDecimal amount, String bankReference, 
                                 LocalDateTime transactionDate, String description) {
        this.bankAccountNumber = bankAccountNumber;
        this.amount = amount;
        this.bankReference = bankReference;
        this.transactionDate = transactionDate;
        this.description = description;
    }

    // Getters and Setters
    public String getBankAccountNumber() {
        return bankAccountNumber;
    }

    public void setBankAccountNumber(String bankAccountNumber) {
        this.bankAccountNumber = bankAccountNumber;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getBankReference() {
        return bankReference;
    }

    public void setBankReference(String bankReference) {
        this.bankReference = bankReference;
    }

    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(LocalDateTime transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}

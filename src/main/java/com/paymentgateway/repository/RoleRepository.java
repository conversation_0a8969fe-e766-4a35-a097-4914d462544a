package com.paymentgateway.repository;

import com.paymentgateway.entity.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface RoleRepository extends JpaRepository<Role, UUID> {

    Optional<Role> findByName(String name);

    boolean existsByName(String name);

    List<Role> findByIsActiveTrue();

    @Query("SELECT r FROM Role r WHERE r.isActive = true AND " +
           "LOWER(r.name) LIKE LOWER(CONCAT('%', :search, '%'))")
    List<Role> findActiveRolesWithSearch(@Param("search") String search);

    @Query("SELECT r FROM Role r JOIN r.permissions p WHERE p.name = :permissionName")
    List<Role> findByPermissionName(@Param("permissionName") String permissionName);
}

package com.paymentgateway.repository;

import com.paymentgateway.entity.Agent;
import com.paymentgateway.entity.Merchant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface MerchantRepository extends JpaRepository<Merchant, UUID> {

    Optional<Merchant> findByMerchantCode(String merchantCode);

    Optional<Merchant> findByApiKey(String apiKey);

    boolean existsByMerchantCode(String merchantCode);

    boolean existsByApiKey(String apiKey);

    List<Merchant> findByIsActiveTrue();

    Page<Merchant> findByIsActiveTrue(Pageable pageable);

    List<Merchant> findByAgent(Agent agent);

    List<Merchant> findByAgentAndIsActiveTrue(Agent agent);

    @Query("SELECT m FROM Merchant m WHERE m.isActive = true AND " +
           "(LOWER(m.merchantCode) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(m.name) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(m.businessName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(m.email) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<Merchant> findActiveMerchantsWithSearch(@Param("search") String search, Pageable pageable);

    @Query("SELECT m FROM Merchant m WHERE m.agent.id = :agentId AND m.isActive = true")
    List<Merchant> findByAgentId(@Param("agentId") UUID agentId);

    @Query("SELECT m FROM Merchant m WHERE m.agent.id = :agentId AND m.isActive = true AND " +
           "(LOWER(m.merchantCode) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(m.name) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(m.businessName) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<Merchant> findByAgentIdWithSearch(@Param("agentId") UUID agentId, @Param("search") String search, Pageable pageable);

    @Query("SELECT COUNT(m) FROM Merchant m WHERE m.agent.id = :agentId AND m.isActive = true")
    Long countByAgentId(@Param("agentId") UUID agentId);
}

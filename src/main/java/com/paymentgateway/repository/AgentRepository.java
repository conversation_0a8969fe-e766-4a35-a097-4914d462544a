package com.paymentgateway.repository;

import com.paymentgateway.entity.Agent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface AgentRepository extends JpaRepository<Agent, UUID> {

    Optional<Agent> findByAgentCode(String agentCode);

    boolean existsByAgentCode(String agentCode);

    List<Agent> findByIsActiveTrue();

    Page<Agent> findByIsActiveTrue(Pageable pageable);

    @Query("SELECT a FROM Agent a WHERE a.isActive = true AND " +
           "(LOWER(a.agentCode) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(a.name) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(a.contactPerson) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(a.email) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<Agent> findActiveAgentsWithSearch(@Param("search") String search, Pageable pageable);

    @Query("SELECT a FROM Agent a WHERE a.user.id = :userId")
    Optional<Agent> findByUserId(@Param("userId") UUID userId);
}

package com.paymentgateway.repository;

import com.paymentgateway.entity.BankAccount;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface BankAccountRepository extends JpaRepository<BankAccount, UUID> {

    Optional<BankAccount> findByAccountNumber(String accountNumber);

    boolean existsByAccountNumber(String accountNumber);

    List<BankAccount> findByIsActiveTrue();

    Page<BankAccount> findByIsActiveTrue(Pageable pageable);

    @Query("SELECT b FROM BankAccount b WHERE b.isActive = true AND " +
           "(LOWER(b.accountNumber) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(b.accountName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(b.bankName) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(b.nickname) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<BankAccount> findActiveBankAccountsWithSearch(@Param("search") String search, Pageable pageable);

    List<BankAccount> findByBankName(String bankName);

    @Query("SELECT DISTINCT b.bankName FROM BankAccount b WHERE b.isActive = true ORDER BY b.bankName")
    List<String> findAllBankNames();
}

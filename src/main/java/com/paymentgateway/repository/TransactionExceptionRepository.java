package com.paymentgateway.repository;

import com.paymentgateway.entity.Merchant;
import com.paymentgateway.entity.TransactionException;
import com.paymentgateway.enums.ExceptionStatus;
import com.paymentgateway.enums.ExceptionType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface TransactionExceptionRepository extends JpaRepository<TransactionException, UUID> {

    List<TransactionException> findByType(ExceptionType type);

    List<TransactionException> findByStatus(ExceptionStatus status);

    List<TransactionException> findByMerchant(Merchant merchant);

    Page<TransactionException> findByStatus(ExceptionStatus status, Pageable pageable);

    Page<TransactionException> findByType(ExceptionType type, Pageable pageable);

    Page<TransactionException> findByMerchant(Merchant merchant, Pageable pageable);

    @Query("SELECT e FROM TransactionException e WHERE e.status = 'OPEN' ORDER BY e.createdAt ASC")
    List<TransactionException> findOpenExceptions();

    @Query("SELECT e FROM TransactionException e WHERE e.createdAt BETWEEN :startDate AND :endDate")
    List<TransactionException> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate,
                                                      @Param("endDate") LocalDateTime endDate);

    @Query("SELECT COUNT(e) FROM TransactionException e WHERE e.status = 'OPEN'")
    Long countOpenExceptions();

    @Query("SELECT COUNT(e) FROM TransactionException e WHERE e.type = :type AND e.status = 'OPEN'")
    Long countOpenExceptionsByType(@Param("type") ExceptionType type);

    @Query("SELECT COUNT(e) FROM TransactionException e WHERE e.merchant = :merchant AND e.status = 'OPEN'")
    Long countOpenExceptionsByMerchant(@Param("merchant") Merchant merchant);

    @Query("SELECT e FROM TransactionException e WHERE " +
           "(:type IS NULL OR e.type = :type) AND " +
           "(:status IS NULL OR e.status = :status) AND " +
           "(:merchantId IS NULL OR e.merchant.id = :merchantId) AND " +
           "(:startDate IS NULL OR e.createdAt >= :startDate) AND " +
           "(:endDate IS NULL OR e.createdAt <= :endDate)")
    Page<TransactionException> findWithFilters(@Param("type") ExceptionType type,
                                               @Param("status") ExceptionStatus status,
                                               @Param("merchantId") UUID merchantId,
                                               @Param("startDate") LocalDateTime startDate,
                                               @Param("endDate") LocalDateTime endDate,
                                               Pageable pageable);
}

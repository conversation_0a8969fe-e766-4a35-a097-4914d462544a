package com.paymentgateway.repository;

import com.paymentgateway.entity.Merchant;
import com.paymentgateway.entity.PayoutRequest;
import com.paymentgateway.enums.PayoutStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface PayoutRequestRepository extends JpaRepository<PayoutRequest, UUID> {

    List<PayoutRequest> findByMerchant(Merchant merchant);

    List<PayoutRequest> findByStatus(PayoutStatus status);

    Page<PayoutRequest> findByMerchant(Merchant merchant, Pageable pageable);

    Page<PayoutRequest> findByStatus(PayoutStatus status, Pageable pageable);

    @Query("SELECT p FROM PayoutRequest p WHERE p.status = 'PENDING' ORDER BY p.requestedAt ASC")
    List<PayoutRequest> findPendingRequests();

    @Query("SELECT p FROM PayoutRequest p WHERE p.requestedAt BETWEEN :startDate AND :endDate")
    List<PayoutRequest> findByRequestedAtBetween(@Param("startDate") LocalDateTime startDate,
                                                 @Param("endDate") LocalDateTime endDate);

    @Query("SELECT p FROM PayoutRequest p WHERE p.merchant = :merchant AND p.requestedAt BETWEEN :startDate AND :endDate")
    List<PayoutRequest> findByMerchantAndRequestedAtBetween(@Param("merchant") Merchant merchant,
                                                            @Param("startDate") LocalDateTime startDate,
                                                            @Param("endDate") LocalDateTime endDate);

    @Query("SELECT SUM(p.amount) FROM PayoutRequest p WHERE p.merchant = :merchant AND p.status = 'COMPLETED'")
    BigDecimal getTotalPayoutsByMerchant(@Param("merchant") Merchant merchant);

    @Query("SELECT COUNT(p) FROM PayoutRequest p WHERE p.status = 'PENDING'")
    Long countPendingRequests();

    @Query("SELECT COUNT(p) FROM PayoutRequest p WHERE p.merchant = :merchant AND p.status = 'PENDING'")
    Long countPendingRequestsByMerchant(@Param("merchant") Merchant merchant);

    @Query("SELECT p FROM PayoutRequest p WHERE " +
           "(:merchantId IS NULL OR p.merchant.id = :merchantId) AND " +
           "(:status IS NULL OR p.status = :status) AND " +
           "(:startDate IS NULL OR p.requestedAt >= :startDate) AND " +
           "(:endDate IS NULL OR p.requestedAt <= :endDate)")
    Page<PayoutRequest> findWithFilters(@Param("merchantId") UUID merchantId,
                                        @Param("status") PayoutStatus status,
                                        @Param("startDate") LocalDateTime startDate,
                                        @Param("endDate") LocalDateTime endDate,
                                        Pageable pageable);
}

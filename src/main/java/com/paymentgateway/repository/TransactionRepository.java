package com.paymentgateway.repository;

import com.paymentgateway.entity.Agent;
import com.paymentgateway.entity.BankAccount;
import com.paymentgateway.entity.Merchant;
import com.paymentgateway.entity.Transaction;
import com.paymentgateway.enums.TransactionStatus;
import com.paymentgateway.enums.TransactionType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface TransactionRepository extends JpaRepository<Transaction, UUID> {

    Optional<Transaction> findByTransactionReference(String transactionReference);

    Optional<Transaction> findByExternalReference(String externalReference);

    boolean existsByTransactionReference(String transactionReference);

    boolean existsByExternalReference(String externalReference);

    List<Transaction> findByMerchant(Merchant merchant);

    List<Transaction> findByAgent(Agent agent);

    List<Transaction> findByBankAccount(BankAccount bankAccount);

    List<Transaction> findByStatus(TransactionStatus status);

    List<Transaction> findByType(TransactionType type);

    Page<Transaction> findByMerchant(Merchant merchant, Pageable pageable);

    Page<Transaction> findByAgent(Agent agent, Pageable pageable);

    @Query("SELECT t FROM Transaction t WHERE t.transactionDate BETWEEN :startDate AND :endDate")
    List<Transaction> findByTransactionDateBetween(@Param("startDate") LocalDateTime startDate, 
                                                   @Param("endDate") LocalDateTime endDate);

    @Query("SELECT t FROM Transaction t WHERE t.merchant = :merchant AND t.transactionDate BETWEEN :startDate AND :endDate")
    List<Transaction> findByMerchantAndTransactionDateBetween(@Param("merchant") Merchant merchant,
                                                              @Param("startDate") LocalDateTime startDate,
                                                              @Param("endDate") LocalDateTime endDate);

    @Query("SELECT t FROM Transaction t WHERE t.agent = :agent AND t.transactionDate BETWEEN :startDate AND :endDate")
    List<Transaction> findByAgentAndTransactionDateBetween(@Param("agent") Agent agent,
                                                           @Param("startDate") LocalDateTime startDate,
                                                           @Param("endDate") LocalDateTime endDate);

    @Query("SELECT t FROM Transaction t WHERE t.isExpected = true AND t.isBankConfirmed = false")
    List<Transaction> findUnpaidTransactions();

    @Query("SELECT t FROM Transaction t WHERE t.isExpected = false AND t.isBankConfirmed = true")
    List<Transaction> findUnrecognizedTransactions();

    @Query("SELECT t FROM Transaction t WHERE t.isExpected = true AND t.isBankConfirmed = true AND t.matchedAt IS NULL")
    List<Transaction> findUnmatchedTransactions();

    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.merchant = :merchant AND t.status = 'COMPLETED'")
    BigDecimal getTotalAmountByMerchant(@Param("merchant") Merchant merchant);

    @Query("SELECT SUM(t.commissionAmount) FROM Transaction t WHERE t.agent = :agent AND t.status = 'COMPLETED'")
    BigDecimal getTotalCommissionByAgent(@Param("agent") Agent agent);

    @Query("SELECT COUNT(t) FROM Transaction t WHERE t.merchant = :merchant AND t.transactionDate BETWEEN :startDate AND :endDate")
    Long countByMerchantAndDateRange(@Param("merchant") Merchant merchant,
                                     @Param("startDate") LocalDateTime startDate,
                                     @Param("endDate") LocalDateTime endDate);

    @Query("SELECT t FROM Transaction t WHERE " +
           "(:merchantId IS NULL OR t.merchant.id = :merchantId) AND " +
           "(:agentId IS NULL OR t.agent.id = :agentId) AND " +
           "(:bankAccountId IS NULL OR t.bankAccount.id = :bankAccountId) AND " +
           "(:status IS NULL OR t.status = :status) AND " +
           "(:type IS NULL OR t.type = :type) AND " +
           "(:startDate IS NULL OR t.transactionDate >= :startDate) AND " +
           "(:endDate IS NULL OR t.transactionDate <= :endDate)")
    Page<Transaction> findWithFilters(@Param("merchantId") UUID merchantId,
                                      @Param("agentId") UUID agentId,
                                      @Param("bankAccountId") UUID bankAccountId,
                                      @Param("status") TransactionStatus status,
                                      @Param("type") TransactionType type,
                                      @Param("startDate") LocalDateTime startDate,
                                      @Param("endDate") LocalDateTime endDate,
                                      Pageable pageable);
}

package com.paymentgateway.repository;

import com.paymentgateway.entity.Agent;
import com.paymentgateway.entity.Commission;
import com.paymentgateway.entity.Merchant;
import com.paymentgateway.enums.CommissionType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface CommissionRepository extends JpaRepository<Commission, UUID> {

    List<Commission> findByMerchant(Merchant merchant);

    List<Commission> findByAgent(Agent agent);

    List<Commission> findByType(CommissionType type);

    Page<Commission> findByMerchant(Merchant merchant, Pageable pageable);

    Page<Commission> findByAgent(Agent agent, Pageable pageable);

    @Query("SELECT c FROM Commission c WHERE c.createdAt BETWEEN :startDate AND :endDate")
    List<Commission> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate,
                                            @Param("endDate") LocalDateTime endDate);

    @Query("SELECT c FROM Commission c WHERE c.merchant = :merchant AND c.createdAt BETWEEN :startDate AND :endDate")
    List<Commission> findByMerchantAndCreatedAtBetween(@Param("merchant") Merchant merchant,
                                                       @Param("startDate") LocalDateTime startDate,
                                                       @Param("endDate") LocalDateTime endDate);

    @Query("SELECT c FROM Commission c WHERE c.agent = :agent AND c.createdAt BETWEEN :startDate AND :endDate")
    List<Commission> findByAgentAndCreatedAtBetween(@Param("agent") Agent agent,
                                                    @Param("startDate") LocalDateTime startDate,
                                                    @Param("endDate") LocalDateTime endDate);

    @Query("SELECT SUM(c.merchantCommission) FROM Commission c WHERE c.merchant = :merchant")
    BigDecimal getTotalMerchantCommission(@Param("merchant") Merchant merchant);

    @Query("SELECT SUM(c.agentCommission) FROM Commission c WHERE c.agent = :agent")
    BigDecimal getTotalAgentCommission(@Param("agent") Agent agent);

    @Query("SELECT SUM(c.platformCommission) FROM Commission c")
    BigDecimal getTotalPlatformCommission();

    @Query("SELECT SUM(c.merchantCommission) FROM Commission c WHERE c.merchant = :merchant AND c.createdAt BETWEEN :startDate AND :endDate")
    BigDecimal getMerchantCommissionByDateRange(@Param("merchant") Merchant merchant,
                                                @Param("startDate") LocalDateTime startDate,
                                                @Param("endDate") LocalDateTime endDate);

    @Query("SELECT SUM(c.agentCommission) FROM Commission c WHERE c.agent = :agent AND c.createdAt BETWEEN :startDate AND :endDate")
    BigDecimal getAgentCommissionByDateRange(@Param("agent") Agent agent,
                                             @Param("startDate") LocalDateTime startDate,
                                             @Param("endDate") LocalDateTime endDate);

    @Query("SELECT c FROM Commission c WHERE " +
           "(:merchantId IS NULL OR c.merchant.id = :merchantId) AND " +
           "(:agentId IS NULL OR c.agent.id = :agentId) AND " +
           "(:type IS NULL OR c.type = :type) AND " +
           "(:startDate IS NULL OR c.createdAt >= :startDate) AND " +
           "(:endDate IS NULL OR c.createdAt <= :endDate)")
    Page<Commission> findWithFilters(@Param("merchantId") UUID merchantId,
                                     @Param("agentId") UUID agentId,
                                     @Param("type") CommissionType type,
                                     @Param("startDate") LocalDateTime startDate,
                                     @Param("endDate") LocalDateTime endDate,
                                     Pageable pageable);
}

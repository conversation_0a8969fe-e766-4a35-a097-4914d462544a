server:
  port: ${SERVER_PORT:8080}

spring:
  application:
    name: payment-gateway
  
  datasource:
    url: ${DATABASE_URL:*****************************************************************}
    username: ${DATABASE_USERNAME:root}
    password: ${DATABASE_PASSWORD:}
    driver-class-name: org.postgresql.Driver
    
  jpa:
    hibernate:
      ddl-auto: ${DDL_AUTO:update}
    show-sql: ${SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        
  security:
    user:
      name: ${ADMIN_USERNAME:admin}
      password: ${ADMIN_PASSWORD:admin123}

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:mySecretKey}
  expiration: ${JWT_EXPIRATION:7200000} # 2 hours in milliseconds
  
# API Configuration
api:
  rate-limit:
    requests-per-minute: ${API_RATE_LIMIT:100}

# Logging
logging:
  level:
    com.paymentgateway: ${LOG_LEVEL:INFO}
    org.springframework.security: ${SECURITY_LOG_LEVEL:WARN}
    org.hibernate.SQL: ${SQL_LOG_LEVEL:WARN}
    
# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# OpenAPI Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
